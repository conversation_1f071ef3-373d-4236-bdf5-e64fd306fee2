#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器
用于保存和加载用户设置
"""

import json
import os
from pathlib import Path
from loguru import logger

class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        self.config_dir = Path(__file__).parent.parent / "config"
        self.config_file = self.config_dir / "user_settings.json"
        self.config_dir.mkdir(exist_ok=True)
        
        # 默认配置
        self.default_config = {
            'download_dir': str(Path.home() / "Downloads" / "Literature"),
            'custom_browser_path': '',
            'use_custom_browser': False,
            'window_size': '1000x700',
            'last_pdf_dir': str(Path.home()),
            'ocr_language': 'eng',
        }
        
        self.config = self.load_config()
    
    def load_config(self) -> dict:
        """加载配置"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # 合并默认配置（确保新增的配置项有默认值）
                merged_config = self.default_config.copy()
                merged_config.update(config)
                
                logger.info("配置加载成功")
                return merged_config
            else:
                logger.info("配置文件不存在，使用默认配置")
                return self.default_config.copy()
                
        except Exception as e:
            logger.error(f"配置加载失败: {e}")
            return self.default_config.copy()
    
    def save_config(self, config: dict = None):
        """保存配置"""
        try:
            if config:
                self.config.update(config)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            
            logger.info("配置保存成功")
            return True
            
        except Exception as e:
            logger.error(f"配置保存失败: {e}")
            return False
    
    def get(self, key: str, default=None):
        """获取配置项"""
        return self.config.get(key, default)
    
    def set(self, key: str, value):
        """设置配置项"""
        self.config[key] = value
    
    def get_browser_config(self) -> dict:
        """获取浏览器配置"""
        return {
            'custom_browser_path': self.get('custom_browser_path', ''),
            'use_custom_browser': self.get('use_custom_browser', False)
        }
    
    def set_browser_config(self, browser_path: str, use_custom: bool):
        """设置浏览器配置"""
        self.set('custom_browser_path', browser_path)
        self.set('use_custom_browser', use_custom)
        self.save_config()
    
    def get_download_dir(self) -> str:
        """获取下载目录"""
        return self.get('download_dir', str(Path.home() / "Downloads" / "Literature"))
    
    def set_download_dir(self, download_dir: str):
        """设置下载目录"""
        self.set('download_dir', download_dir)
        self.save_config()

# 全局配置管理器实例
config_manager = ConfigManager()
