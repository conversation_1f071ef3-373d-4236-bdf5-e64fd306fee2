namespace EnhancedStaticRockMechanicsAnalysisSystem.Forms
{
    partial class StaticRockMechanicsForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.Windows.Forms.DataVisualization.Charting.ChartArea chartArea1 = new System.Windows.Forms.DataVisualization.Charting.ChartArea();
            System.Windows.Forms.DataVisualization.Charting.Legend legend1 = new System.Windows.Forms.DataVisualization.Charting.Legend();
            System.Windows.Forms.DataVisualization.Charting.Series series1 = new System.Windows.Forms.DataVisualization.Charting.Series();
            this.lblTitle = new System.Windows.Forms.Label();
            this.lblWelcome = new System.Windows.Forms.Label();
            this.btnBack = new System.Windows.Forms.Button();
            this.btnLogout = new System.Windows.Forms.Button();
            this.btnEmergencyExit = new System.Windows.Forms.Button();
            this.btnViewComparison = new System.Windows.Forms.Button();
            this.btnEnhancedAnalysis = new System.Windows.Forms.Button();
            this.pnlParameters = new System.Windows.Forms.Panel();
            this.lblCalculationResult = new System.Windows.Forms.Label();
            this.btnCalculate = new System.Windows.Forms.Button();
            this.txtVs = new System.Windows.Forms.TextBox();
            this.lblVs = new System.Windows.Forms.Label();
            this.txtVp = new System.Windows.Forms.TextBox();
            this.lblVp = new System.Windows.Forms.Label();
            this.txtDensity = new System.Windows.Forms.TextBox();
            this.lblDensity = new System.Windows.Forms.Label();
            this.lblParametersTitle = new System.Windows.Forms.Label();
            this.pnlData = new System.Windows.Forms.Panel();
            this.dgvMechanicsData = new System.Windows.Forms.DataGridView();
            this.btnExport = new System.Windows.Forms.Button();
            this.btnImport = new System.Windows.Forms.Button();
            this.lblDataTitle = new System.Windows.Forms.Label();
            this.pnlChart = new System.Windows.Forms.Panel();
            this.chartBrittleness = new System.Windows.Forms.DataVisualization.Charting.Chart();
            this.btnSaveCurve = new System.Windows.Forms.Button();
            this.btnReset = new System.Windows.Forms.Button();
            this.btnGenerateCurve = new System.Windows.Forms.Button();
            this.lblChartTitle = new System.Windows.Forms.Label();
            this.pnlParameters.SuspendLayout();
            this.pnlData.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvMechanicsData)).BeginInit();
            this.pnlChart.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartBrittleness)).BeginInit();
            this.SuspendLayout();
            // 
            // lblTitle
            // 
            this.lblTitle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(45)))), ((int)(((byte)(45)))));
            this.lblTitle.Dock = System.Windows.Forms.DockStyle.Top;
            this.lblTitle.Font = new System.Drawing.Font("微软雅黑", 18F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblTitle.ForeColor = System.Drawing.Color.Cyan;
            this.lblTitle.Location = new System.Drawing.Point(0, 0);
            this.lblTitle.Margin = new System.Windows.Forms.Padding(6, 0, 6, 0);
            this.lblTitle.Name = "lblTitle";
            this.lblTitle.Size = new System.Drawing.Size(1600, 80);
            this.lblTitle.TabIndex = 0;
            this.lblTitle.Text = "静态岩石力学参数法脆性指数计算系统";
            this.lblTitle.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // lblWelcome
            // 
            this.lblWelcome.AutoSize = true;
            this.lblWelcome.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblWelcome.ForeColor = System.Drawing.Color.LightGray;
            this.lblWelcome.Location = new System.Drawing.Point(20, 90);
            this.lblWelcome.Name = "lblWelcome";
            this.lblWelcome.Size = new System.Drawing.Size(326, 21);
            this.lblWelcome.TabIndex = 1;
            this.lblWelcome.Text = "欢迎使用静态岩石力学参数法";
            // 
            // btnBack
            // 
            this.btnBack.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(50)))), ((int)(((byte)(50)))), ((int)(((byte)(50)))));
            this.btnBack.FlatAppearance.BorderColor = System.Drawing.Color.Cyan;
            this.btnBack.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnBack.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.btnBack.ForeColor = System.Drawing.Color.LightSkyBlue;
            this.btnBack.Location = new System.Drawing.Point(20, 120);
            this.btnBack.Name = "btnBack";
            this.btnBack.Size = new System.Drawing.Size(100, 40);
            this.btnBack.TabIndex = 2;
            this.btnBack.Text = "返回主界面";
            this.btnBack.UseVisualStyleBackColor = false;
            this.btnBack.Click += new System.EventHandler(this.btnBack_Click);
            // 
            // btnLogout
            // 
            this.btnLogout.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(50)))), ((int)(((byte)(50)))), ((int)(((byte)(50)))));
            this.btnLogout.FlatAppearance.BorderColor = System.Drawing.Color.Cyan;
            this.btnLogout.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnLogout.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.btnLogout.ForeColor = System.Drawing.Color.LightSkyBlue;
            this.btnLogout.Location = new System.Drawing.Point(140, 120);
            this.btnLogout.Name = "btnLogout";
            this.btnLogout.Size = new System.Drawing.Size(100, 40);
            this.btnLogout.TabIndex = 3;
            this.btnLogout.Text = "退出登录";
            this.btnLogout.UseVisualStyleBackColor = false;
            this.btnLogout.Click += new System.EventHandler(this.btnLogout_Click);
            // 
            // btnEmergencyExit
            // 
            this.btnEmergencyExit.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(50)))), ((int)(((byte)(50)))), ((int)(((byte)(50)))));
            this.btnEmergencyExit.FlatAppearance.BorderColor = System.Drawing.Color.Red;
            this.btnEmergencyExit.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnEmergencyExit.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.btnEmergencyExit.ForeColor = System.Drawing.Color.Red;
            this.btnEmergencyExit.Location = new System.Drawing.Point(260, 120);
            this.btnEmergencyExit.Name = "btnEmergencyExit";
            this.btnEmergencyExit.Size = new System.Drawing.Size(100, 40);
            this.btnEmergencyExit.TabIndex = 4;
            this.btnEmergencyExit.Text = "紧急退出";
            this.btnEmergencyExit.UseVisualStyleBackColor = false;
            this.btnEmergencyExit.Click += new System.EventHandler(this.btnEmergencyExit_Click);
            // 
            // btnViewComparison
            // 
            this.btnViewComparison.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(50)))), ((int)(((byte)(50)))), ((int)(((byte)(50)))));
            this.btnViewComparison.FlatAppearance.BorderColor = System.Drawing.Color.Cyan;
            this.btnViewComparison.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnViewComparison.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.btnViewComparison.ForeColor = System.Drawing.Color.LightSkyBlue;
            this.btnViewComparison.Location = new System.Drawing.Point(380, 120);
            this.btnViewComparison.Name = "btnViewComparison";
            this.btnViewComparison.Size = new System.Drawing.Size(100, 40);
            this.btnViewComparison.TabIndex = 5;
            this.btnViewComparison.Text = "查看对比图";
            this.btnViewComparison.UseVisualStyleBackColor = false;
            this.btnViewComparison.Click += new System.EventHandler(this.btnViewComparison_Click);
            // 
            // btnEnhancedAnalysis
            // 
            this.btnEnhancedAnalysis.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(120)))), ((int)(((byte)(215)))));
            this.btnEnhancedAnalysis.FlatAppearance.BorderColor = System.Drawing.Color.Cyan;
            this.btnEnhancedAnalysis.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnEnhancedAnalysis.Font = new System.Drawing.Font("微软雅黑", 10F, System.Drawing.FontStyle.Bold);
            this.btnEnhancedAnalysis.ForeColor = System.Drawing.Color.White;
            this.btnEnhancedAnalysis.Location = new System.Drawing.Point(500, 120);
            this.btnEnhancedAnalysis.Name = "btnEnhancedAnalysis";
            this.btnEnhancedAnalysis.Size = new System.Drawing.Size(120, 40);
            this.btnEnhancedAnalysis.TabIndex = 6;
            this.btnEnhancedAnalysis.Text = "增强分析算法";
            this.btnEnhancedAnalysis.UseVisualStyleBackColor = false;
            this.btnEnhancedAnalysis.Click += new System.EventHandler(this.btnEnhancedAnalysis_Click);
            //
            // pnlParameters
            //
            this.pnlParameters.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(45)))), ((int)(((byte)(45)))));
            this.pnlParameters.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.pnlParameters.Controls.Add(this.lblCalculationResult);
            this.pnlParameters.Controls.Add(this.btnCalculate);
            this.pnlParameters.Controls.Add(this.txtVs);
            this.pnlParameters.Controls.Add(this.lblVs);
            this.pnlParameters.Controls.Add(this.txtVp);
            this.pnlParameters.Controls.Add(this.lblVp);
            this.pnlParameters.Controls.Add(this.txtDensity);
            this.pnlParameters.Controls.Add(this.lblDensity);
            this.pnlParameters.Controls.Add(this.lblParametersTitle);
            this.pnlParameters.Location = new System.Drawing.Point(20, 180);
            this.pnlParameters.Name = "pnlParameters";
            this.pnlParameters.Size = new System.Drawing.Size(1560, 150);
            this.pnlParameters.TabIndex = 7;
            //
            // lblParametersTitle
            //
            this.lblParametersTitle.AutoSize = true;
            this.lblParametersTitle.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Bold);
            this.lblParametersTitle.ForeColor = System.Drawing.Color.White;
            this.lblParametersTitle.Location = new System.Drawing.Point(20, 20);
            this.lblParametersTitle.Name = "lblParametersTitle";
            this.lblParametersTitle.Size = new System.Drawing.Size(138, 22);
            this.lblParametersTitle.TabIndex = 0;
            this.lblParametersTitle.Text = "参数输入与计算";
            //
            // lblDensity
            //
            this.lblDensity.AutoSize = true;
            this.lblDensity.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.lblDensity.ForeColor = System.Drawing.Color.White;
            this.lblDensity.Location = new System.Drawing.Point(20, 60);
            this.lblDensity.Name = "lblDensity";
            this.lblDensity.Size = new System.Drawing.Size(107, 20);
            this.lblDensity.TabIndex = 1;
            this.lblDensity.Text = "密度(g/cm³)：";
            //
            // txtDensity
            //
            this.txtDensity.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(60)))), ((int)(((byte)(60)))));
            this.txtDensity.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.txtDensity.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.txtDensity.ForeColor = System.Drawing.Color.White;
            this.txtDensity.Location = new System.Drawing.Point(130, 58);
            this.txtDensity.Name = "txtDensity";
            this.txtDensity.Size = new System.Drawing.Size(100, 25);
            this.txtDensity.TabIndex = 2;
            //
            // lblVp
            //
            this.lblVp.AutoSize = true;
            this.lblVp.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.lblVp.ForeColor = System.Drawing.Color.White;
            this.lblVp.Location = new System.Drawing.Point(260, 60);
            this.lblVp.Name = "lblVp";
            this.lblVp.Size = new System.Drawing.Size(135, 20);
            this.lblVp.TabIndex = 3;
            this.lblVp.Text = "纵波速度(m/s)：";
            //
            // txtVp
            //
            this.txtVp.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(60)))), ((int)(((byte)(60)))));
            this.txtVp.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.txtVp.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.txtVp.ForeColor = System.Drawing.Color.White;
            this.txtVp.Location = new System.Drawing.Point(400, 58);
            this.txtVp.Name = "txtVp";
            this.txtVp.Size = new System.Drawing.Size(100, 25);
            this.txtVp.TabIndex = 4;
            //
            // lblVs
            //
            this.lblVs.AutoSize = true;
            this.lblVs.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.lblVs.ForeColor = System.Drawing.Color.White;
            this.lblVs.Location = new System.Drawing.Point(530, 60);
            this.lblVs.Name = "lblVs";
            this.lblVs.Size = new System.Drawing.Size(135, 20);
            this.lblVs.TabIndex = 5;
            this.lblVs.Text = "横波速度(m/s)：";
            //
            // txtVs
            //
            this.txtVs.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(60)))), ((int)(((byte)(60)))));
            this.txtVs.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.txtVs.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.txtVs.ForeColor = System.Drawing.Color.White;
            this.txtVs.Location = new System.Drawing.Point(670, 58);
            this.txtVs.Name = "txtVs";
            this.txtVs.Size = new System.Drawing.Size(100, 25);
            this.txtVs.TabIndex = 6;
            //
            // btnCalculate
            //
            this.btnCalculate.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(50)))), ((int)(((byte)(50)))), ((int)(((byte)(50)))));
            this.btnCalculate.FlatAppearance.BorderColor = System.Drawing.Color.Cyan;
            this.btnCalculate.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnCalculate.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.btnCalculate.ForeColor = System.Drawing.Color.LightSkyBlue;
            this.btnCalculate.Location = new System.Drawing.Point(800, 55);
            this.btnCalculate.Name = "btnCalculate";
            this.btnCalculate.Size = new System.Drawing.Size(120, 30);
            this.btnCalculate.TabIndex = 7;
            this.btnCalculate.Text = "计算脆性指数";
            this.btnCalculate.UseVisualStyleBackColor = false;
            this.btnCalculate.Click += new System.EventHandler(this.btnCalculate_Click);
            //
            // lblCalculationResult
            //
            this.lblCalculationResult.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.lblCalculationResult.ForeColor = System.Drawing.Color.Yellow;
            this.lblCalculationResult.Location = new System.Drawing.Point(20, 100);
            this.lblCalculationResult.Name = "lblCalculationResult";
            this.lblCalculationResult.Size = new System.Drawing.Size(1500, 30);
            this.lblCalculationResult.TabIndex = 8;
            this.lblCalculationResult.Text = "计算结果将在此显示";
            this.lblCalculationResult.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            //
            // pnlData
            //
            this.pnlData.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(45)))), ((int)(((byte)(45)))));
            this.pnlData.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.pnlData.Controls.Add(this.dgvMechanicsData);
            this.pnlData.Controls.Add(this.btnExport);
            this.pnlData.Controls.Add(this.btnImport);
            this.pnlData.Controls.Add(this.lblDataTitle);
            this.pnlData.Location = new System.Drawing.Point(20, 350);
            this.pnlData.Name = "pnlData";
            this.pnlData.Size = new System.Drawing.Size(780, 400);
            this.pnlData.TabIndex = 8;
            //
            // lblDataTitle
            //
            this.lblDataTitle.AutoSize = true;
            this.lblDataTitle.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Bold);
            this.lblDataTitle.ForeColor = System.Drawing.Color.White;
            this.lblDataTitle.Location = new System.Drawing.Point(20, 20);
            this.lblDataTitle.Name = "lblDataTitle";
            this.lblDataTitle.Size = new System.Drawing.Size(74, 22);
            this.lblDataTitle.TabIndex = 0;
            this.lblDataTitle.Text = "数据管理";
            //
            // btnImport
            //
            this.btnImport.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(50)))), ((int)(((byte)(50)))), ((int)(((byte)(50)))));
            this.btnImport.FlatAppearance.BorderColor = System.Drawing.Color.Cyan;
            this.btnImport.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnImport.Font = new System.Drawing.Font("微软雅黑", 9F);
            this.btnImport.ForeColor = System.Drawing.Color.LightSkyBlue;
            this.btnImport.Location = new System.Drawing.Point(600, 15);
            this.btnImport.Name = "btnImport";
            this.btnImport.Size = new System.Drawing.Size(80, 30);
            this.btnImport.TabIndex = 1;
            this.btnImport.Text = "导入数据";
            this.btnImport.UseVisualStyleBackColor = false;
            this.btnImport.Click += new System.EventHandler(this.btnImportData_Click);
            //
            // btnExport
            //
            this.btnExport.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(50)))), ((int)(((byte)(50)))), ((int)(((byte)(50)))));
            this.btnExport.FlatAppearance.BorderColor = System.Drawing.Color.Cyan;
            this.btnExport.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnExport.Font = new System.Drawing.Font("微软雅黑", 9F);
            this.btnExport.ForeColor = System.Drawing.Color.LightSkyBlue;
            this.btnExport.Location = new System.Drawing.Point(690, 15);
            this.btnExport.Name = "btnExport";
            this.btnExport.Size = new System.Drawing.Size(80, 30);
            this.btnExport.TabIndex = 2;
            this.btnExport.Text = "导出数据";
            this.btnExport.UseVisualStyleBackColor = false;
            this.btnExport.Click += new System.EventHandler(this.btnExportData_Click);
            //
            // dgvMechanicsData
            //
            this.dgvMechanicsData.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(60)))), ((int)(((byte)(60)))));
            this.dgvMechanicsData.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgvMechanicsData.Location = new System.Drawing.Point(20, 55);
            this.dgvMechanicsData.Name = "dgvMechanicsData";
            this.dgvMechanicsData.RowTemplate.Height = 25;
            this.dgvMechanicsData.Size = new System.Drawing.Size(750, 330);
            this.dgvMechanicsData.TabIndex = 3;
            //
            // pnlChart
            //
            this.pnlChart.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(45)))), ((int)(((byte)(45)))));
            this.pnlChart.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.pnlChart.Controls.Add(this.chartBrittleness);
            this.pnlChart.Controls.Add(this.btnSaveCurve);
            this.pnlChart.Controls.Add(this.btnReset);
            this.pnlChart.Controls.Add(this.btnGenerateCurve);
            this.pnlChart.Controls.Add(this.lblChartTitle);
            this.pnlChart.Location = new System.Drawing.Point(820, 350);
            this.pnlChart.Name = "pnlChart";
            this.pnlChart.Size = new System.Drawing.Size(760, 400);
            this.pnlChart.TabIndex = 9;
            //
            // lblChartTitle
            //
            this.lblChartTitle.AutoSize = true;
            this.lblChartTitle.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Bold);
            this.lblChartTitle.ForeColor = System.Drawing.Color.White;
            this.lblChartTitle.Location = new System.Drawing.Point(20, 20);
            this.lblChartTitle.Name = "lblChartTitle";
            this.lblChartTitle.Size = new System.Drawing.Size(138, 22);
            this.lblChartTitle.TabIndex = 0;
            this.lblChartTitle.Text = "脆性指数趋势图";
            //
            // btnGenerateCurve
            //
            this.btnGenerateCurve.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(50)))), ((int)(((byte)(50)))), ((int)(((byte)(50)))));
            this.btnGenerateCurve.FlatAppearance.BorderColor = System.Drawing.Color.Cyan;
            this.btnGenerateCurve.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnGenerateCurve.Font = new System.Drawing.Font("微软雅黑", 9F);
            this.btnGenerateCurve.ForeColor = System.Drawing.Color.LightSkyBlue;
            this.btnGenerateCurve.Location = new System.Drawing.Point(500, 15);
            this.btnGenerateCurve.Name = "btnGenerateCurve";
            this.btnGenerateCurve.Size = new System.Drawing.Size(80, 30);
            this.btnGenerateCurve.TabIndex = 1;
            this.btnGenerateCurve.Text = "生成曲线";
            this.btnGenerateCurve.UseVisualStyleBackColor = false;
            //
            // btnReset
            //
            this.btnReset.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(50)))), ((int)(((byte)(50)))), ((int)(((byte)(50)))));
            this.btnReset.FlatAppearance.BorderColor = System.Drawing.Color.Cyan;
            this.btnReset.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnReset.Font = new System.Drawing.Font("微软雅黑", 9F);
            this.btnReset.ForeColor = System.Drawing.Color.LightSkyBlue;
            this.btnReset.Location = new System.Drawing.Point(590, 15);
            this.btnReset.Name = "btnReset";
            this.btnReset.Size = new System.Drawing.Size(80, 30);
            this.btnReset.TabIndex = 2;
            this.btnReset.Text = "重置";
            this.btnReset.UseVisualStyleBackColor = false;
            this.btnReset.Click += new System.EventHandler(this.btnClearData_Click);
            //
            // btnSaveCurve
            //
            this.btnSaveCurve.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(50)))), ((int)(((byte)(50)))), ((int)(((byte)(50)))));
            this.btnSaveCurve.FlatAppearance.BorderColor = System.Drawing.Color.Cyan;
            this.btnSaveCurve.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnSaveCurve.Font = new System.Drawing.Font("微软雅黑", 9F);
            this.btnSaveCurve.ForeColor = System.Drawing.Color.LightSkyBlue;
            this.btnSaveCurve.Location = new System.Drawing.Point(680, 15);
            this.btnSaveCurve.Name = "btnSaveCurve";
            this.btnSaveCurve.Size = new System.Drawing.Size(80, 30);
            this.btnSaveCurve.TabIndex = 3;
            this.btnSaveCurve.Text = "保存曲线";
            this.btnSaveCurve.UseVisualStyleBackColor = false;
            //
            // chartBrittleness
            //
            chartArea1.Name = "ChartArea1";
            this.chartBrittleness.ChartAreas.Add(chartArea1);
            legend1.Name = "Legend1";
            this.chartBrittleness.Legends.Add(legend1);
            this.chartBrittleness.Location = new System.Drawing.Point(20, 55);
            this.chartBrittleness.Name = "chartBrittleness";
            series1.ChartArea = "ChartArea1";
            series1.Legend = "Legend1";
            series1.Name = "Series1";
            this.chartBrittleness.Series.Add(series1);
            this.chartBrittleness.Size = new System.Drawing.Size(730, 330);
            this.chartBrittleness.TabIndex = 4;
            this.chartBrittleness.Text = "chart1";
            //
            // StaticRockMechanicsForm
            //
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(33)))), ((int)(((byte)(33)))), ((int)(((byte)(33)))));
            this.ClientSize = new System.Drawing.Size(1600, 800);
            this.Controls.Add(this.pnlChart);
            this.Controls.Add(this.pnlData);
            this.Controls.Add(this.pnlParameters);
            this.Controls.Add(this.btnEnhancedAnalysis);
            this.Controls.Add(this.btnViewComparison);
            this.Controls.Add(this.btnEmergencyExit);
            this.Controls.Add(this.btnLogout);
            this.Controls.Add(this.btnBack);
            this.Controls.Add(this.lblWelcome);
            this.Controls.Add(this.lblTitle);
            this.ForeColor = System.Drawing.Color.White;
            this.Name = "StaticRockMechanicsForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "静态岩石力学参数法 - 脆性指数计算";
            this.WindowState = System.Windows.Forms.FormWindowState.Maximized;
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.StaticRockMechanicsForm_FormClosing);
            this.pnlParameters.ResumeLayout(false);
            this.pnlParameters.PerformLayout();
            this.pnlData.ResumeLayout(false);
            this.pnlData.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvMechanicsData)).EndInit();
            this.pnlChart.ResumeLayout(false);
            this.pnlChart.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartBrittleness)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Label lblTitle;
        private System.Windows.Forms.Label lblWelcome;
        private System.Windows.Forms.Button btnBack;
        private System.Windows.Forms.Button btnLogout;
        private System.Windows.Forms.Button btnEmergencyExit;
        private System.Windows.Forms.Button btnViewComparison;
        private System.Windows.Forms.Button btnEnhancedAnalysis;
        private System.Windows.Forms.Panel pnlParameters;
        private System.Windows.Forms.Label lblCalculationResult;
        private System.Windows.Forms.Button btnCalculate;
        private System.Windows.Forms.TextBox txtVs;
        private System.Windows.Forms.Label lblVs;
        private System.Windows.Forms.TextBox txtVp;
        private System.Windows.Forms.Label lblVp;
        private System.Windows.Forms.TextBox txtDensity;
        private System.Windows.Forms.Label lblDensity;
        private System.Windows.Forms.Label lblParametersTitle;
        private System.Windows.Forms.Panel pnlData;
        private System.Windows.Forms.DataGridView dgvMechanicsData;
        private System.Windows.Forms.Button btnExport;
        private System.Windows.Forms.Button btnImport;
        private System.Windows.Forms.Label lblDataTitle;
        private System.Windows.Forms.Panel pnlChart;
        private System.Windows.Forms.DataVisualization.Charting.Chart chartBrittleness;
        private System.Windows.Forms.Button btnSaveCurve;
        private System.Windows.Forms.Button btnReset;
        private System.Windows.Forms.Button btnGenerateCurve;
        private System.Windows.Forms.Label lblChartTitle;
    }
}
