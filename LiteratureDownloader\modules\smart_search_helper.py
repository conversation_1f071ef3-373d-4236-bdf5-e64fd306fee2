#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能搜索助手
为用户提供优化的搜索建议和自动化搜索
"""

import os
import time
import webbrowser
import tkinter as tk
from tkinter import messagebox
from typing import List, Dict
from loguru import logger

try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.common.keys import Keys
    from selenium.webdriver.common.action_chains import ActionChains
except ImportError as e:
    logger.error(f"Selenium导入失败: {e}")


class SmartSearchHelper:
    """智能搜索助手 - 全自动版本"""

    def __init__(self, download_dir: str, custom_browser_path: str = None):
        self.download_dir = download_dir
        self.custom_browser_path = custom_browser_path
        self.driver = None
        self.captcha_handler = None
    
    def create_browser(self):
        """创建浏览器实例"""
        try:
            chrome_options = Options()

            # 反检测设置
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # 模拟真实用户行为
            chrome_options.add_argument("--disable-web-security")
            chrome_options.add_argument("--disable-features=VizDisplayCompositor")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--no-sandbox")

            # 设置用户代理
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

            # 设置下载目录
            prefs = {
                "download.default_directory": self.download_dir,
                "download.prompt_for_download": False,
                "download.directory_upgrade": True,
                "safebrowsing.enabled": True,
                "profile.default_content_setting_values.notifications": 2,
                "profile.default_content_settings.popups": 0
            }
            chrome_options.add_experimental_option("prefs", prefs)

            # 如果用户指定了自定义浏览器路径，使用它
            if self.custom_browser_path and os.path.exists(self.custom_browser_path):
                chrome_options.binary_location = self.custom_browser_path
                logger.info(f"使用自定义浏览器: {self.custom_browser_path}")

            self.driver = webdriver.Chrome(options=chrome_options)

            # 执行反检测脚本
            self.driver.execute_script("""
                Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
                Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]});
                Object.defineProperty(navigator, 'languages', {get: () => ['zh-CN', 'zh', 'en']});
                window.chrome = {runtime: {}};
            """)

            # 初始化验证码处理器
            try:
                from .captcha_handler import CaptchaHandler
                self.captcha_handler = CaptchaHandler(self.driver)
                logger.info("验证码处理器初始化成功")
            except Exception as e:
                logger.warning(f"验证码处理器初始化失败: {e}")

            logger.info("浏览器创建成功")
            return True

        except Exception as e:
            logger.error(f"创建浏览器失败: {e}")
            return False
    
    def auto_search_and_download(self, citations: List[Dict[str, str]]) -> Dict[str, int]:
        """全自动搜索和下载"""
        results = {
            'total': len(citations),
            'processed': 0,
            'downloaded': 0,
            'failed': 0,
            'skipped': 0
        }

        if not self.create_browser():
            return results

        try:
            for citation in citations:
                ref_num = citation.get('reference_number', '?')
                logger.info(f"开始处理文献 [{ref_num}]")

                # 检查文献信息
                if not self._has_valid_info(citation):
                    results['skipped'] += 1
                    logger.info(f"跳过文献 [{ref_num}]: 信息不足")
                    continue

                results['processed'] += 1

                # 搜索文献
                if self._search_literature(citation):
                    # 下载EndNote引用
                    if self._download_endnote(citation):
                        results['downloaded'] += 1
                        logger.info(f"文献 [{ref_num}] 下载成功")
                    else:
                        results['failed'] += 1
                        logger.warning(f"文献 [{ref_num}] 下载失败")
                else:
                    results['failed'] += 1
                    logger.warning(f"文献 [{ref_num}] 搜索失败")

                # 每个文献之间稍作停顿
                time.sleep(2)

            return results

        except Exception as e:
            logger.error(f"自动搜索下载过程出错: {e}")
            return results

        finally:
            self.close()
    
    def _has_valid_info(self, citation: Dict[str, str]) -> bool:
        """检查文献信息是否足够"""
        title = citation.get('title', '').strip()
        authors = citation.get('authors', '')

        # 至少要有标题或作者信息
        return (title and title != '未识别' and len(title) > 5) or \
               (authors and authors != '未识别')

    def _search_literature(self, citation: Dict[str, str]) -> bool:
        """搜索文献"""
        ref_num = citation.get('reference_number', '?')
        title = citation.get('title', '').strip()
        authors = citation.get('authors', '')

        try:
            # 打开Web of Science搜索页面
            self.driver.get("https://webofscience.clarivate.cn")
            time.sleep(3)

            # 检查是否出现人机验证
            if self.captcha_handler and self.captcha_handler.detect_captcha():
                logger.info(f"文献 [{ref_num}] 遇到人机验证，开始处理...")
                if not self.captcha_handler.handle_captcha():
                    logger.error(f"文献 [{ref_num}] 人机验证处理失败")
                    return False
                logger.info(f"文献 [{ref_num}] 人机验证处理完成")

            # 构建搜索查询
            search_query = self._build_search_query(citation)
            if not search_query:
                return False

            logger.info(f"文献 [{ref_num}] 搜索查询: {search_query}")

            # 查找并填写搜索框
            if not self._fill_search_box(search_query):
                return False

            # 等待搜索结果
            time.sleep(5)

            # 检查是否有搜索结果
            return self._has_search_results()

        except Exception as e:
            logger.error(f"搜索文献 [{ref_num}] 失败: {e}")
            return False

    def _build_search_query(self, citation: Dict[str, str]) -> str:
        """构建搜索查询"""
        title = citation.get('title', '').strip()
        authors = citation.get('authors', '')

        # 优先使用标题关键词
        if title and title != '未识别' and len(title) > 10:
            # 提取关键词，去除常见停用词
            stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were'}
            words = title.lower().replace('"', '').replace("'", "").split()
            keywords = [word for word in words if len(word) > 3 and word not in stop_words]

            if len(keywords) >= 3:
                return ' '.join(keywords[:4])  # 取前4个关键词
            else:
                return title  # 如果关键词太少，使用完整标题

        # 使用作者信息
        elif authors and authors != '未识别':
            if isinstance(authors, list):
                return authors[0] if authors else ""
            else:
                return authors.split(',')[0].strip()

        return ""



    def _fill_search_box(self, search_query: str) -> bool:
        """填写搜索框"""
        # 尝试多种搜索框选择器
        search_selectors = [
            'input[placeholder*="Example"]',
            'input[placeholder*="example"]',
            'input[type="text"]',
            'textarea',
            '#search-input',
            '.search-input',
            'input[data-ta="search-input"]',
            'input[name="search"]',
            'input[id*="search"]',
            'input[class*="search"]'
        ]

        for selector in search_selectors:
            try:
                search_box = WebDriverWait(self.driver, 3).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                )

                # 清空并输入搜索内容
                search_box.clear()
                search_box.send_keys(search_query)

                # 尝试提交搜索
                try:
                    search_box.send_keys(Keys.RETURN)
                except:
                    # 如果回车不行，尝试找搜索按钮
                    self._click_search_button()

                logger.info(f"成功填写搜索框: {selector}")
                return True

            except Exception as e:
                continue

        logger.error("未找到可用的搜索框")
        return False

    def _click_search_button(self):
        """点击搜索按钮"""
        button_selectors = [
            'button[type="submit"]',
            'button[data-ta="search-button"]',
            'button:contains("Search")',
            'button:contains("搜索")',
            '.search-button',
            'input[type="submit"]',
            'button[class*="search"]',
            'button[id*="search"]'
        ]

        for selector in button_selectors:
            try:
                search_btn = self.driver.find_element(By.CSS_SELECTOR, selector)
                search_btn.click()
                return True
            except:
                continue

        return False

    def _has_search_results(self) -> bool:
        """检查是否有搜索结果"""
        result_selectors = [
            '.search-results',
            '.result-item',
            '.search-result',
            '[data-ta="result"]',
            '.title-link',
            'a[href*="record"]',
            '.record-title'
        ]

        for selector in result_selectors:
            try:
                results = self.driver.find_elements(By.CSS_SELECTOR, selector)
                if results:
                    logger.info(f"找到 {len(results)} 个搜索结果")
                    return True
            except:
                continue

        logger.warning("未找到搜索结果")
        return False

    def _download_endnote(self, citation: Dict[str, str]) -> bool:
        """下载EndNote引用"""
        ref_num = citation.get('reference_number', '?')

        try:
            # 点击第一个搜索结果
            if not self._click_first_result():
                return False

            time.sleep(3)

            # 查找并点击导出按钮
            if not self._click_export_button():
                return False

            time.sleep(2)

            # 选择EndNote格式
            if not self._select_endnote_format():
                return False

            time.sleep(3)
            logger.info(f"文献 [{ref_num}] EndNote引用下载完成")
            return True

        except Exception as e:
            logger.error(f"下载EndNote引用失败: {e}")
            return False

    def _click_first_result(self) -> bool:
        """点击第一个搜索结果"""
        result_selectors = [
            '.title-link',
            'a[data-ta="result-title-link"]',
            '.search-results-item a',
            'h3 a',
            '.record-title a',
            'a[href*="record"]'
        ]

        for selector in result_selectors:
            try:
                first_result = WebDriverWait(self.driver, 5).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                )
                first_result.click()
                logger.info("成功点击第一个搜索结果")
                return True
            except:
                continue

        logger.error("未找到可点击的搜索结果")
        return False

    def _click_export_button(self) -> bool:
        """点击导出按钮"""
        export_selectors = [
            'button[data-ta="export-button"]',
            'button[title*="导出"]',
            'button[title*="Export"]',
            'button:contains("导出")',
            'button:contains("Export")',
            '.export-button',
            'a[href*="export"]',
            'button[class*="export"]'
        ]

        for selector in export_selectors:
            try:
                export_btn = WebDriverWait(self.driver, 5).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                )
                self.driver.execute_script("arguments[0].click();", export_btn)
                logger.info("成功点击导出按钮")
                return True
            except:
                continue

        logger.error("未找到导出按钮")
        return False

    def _select_endnote_format(self) -> bool:
        """选择EndNote格式"""
        endnote_selectors = [
            'a[title*="EndNote Desktop"]',
            'button[title*="EndNote Desktop"]',
            'a[data-value="endnote"]',
            'a:contains("EndNote")',
            'button:contains("EndNote")',
            '.endnote-desktop',
            'a[href*="endnote"]'
        ]

        for selector in endnote_selectors:
            try:
                endnote_option = WebDriverWait(self.driver, 5).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                )
                self.driver.execute_script("arguments[0].click();", endnote_option)
                logger.info("成功选择EndNote格式")
                return True
            except:
                continue

        logger.error("未找到EndNote选项")
        return False
    

    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            try:
                self.driver.quit()
            except:
                pass
