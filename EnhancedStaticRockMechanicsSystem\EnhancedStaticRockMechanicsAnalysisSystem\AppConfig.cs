using System;
using System.Configuration;
using System.IO;
using Newtonsoft.Json;

namespace EnhancedStaticRockMechanicsAnalysisSystem
{
    /// <summary>
    /// 应用程序配置管理
    /// </summary>
    public class AppConfig
    {
        private static AppConfig? _instance;
        private static readonly object _lock = new object();
        private readonly string _configFilePath;

        // 配置属性
        public string ApplicationName { get; set; } = "增强版静态岩石力学参数分析系统";
        public string Version { get; set; } = "1.0.0";
        public string DatabaseConnectionString { get; set; } = "";
        public bool EnableLogging { get; set; } = true;
        public int LogRetentionDays { get; set; } = 30;
        public string DefaultExportPath { get; set; } = "";
        public string DefaultImportPath { get; set; } = "";
        public bool AutoSaveEnabled { get; set; } = true;
        public int AutoSaveIntervalMinutes { get; set; } = 5;
        public string Theme { get; set; } = "Dark";
        public string Language { get; set; } = "zh-CN";

        // 计算参数配置
        public double DefaultDensityMin { get; set; } = 1.5;
        public double DefaultDensityMax { get; set; } = 5.0;
        public double DefaultVpMin { get; set; } = 1000;
        public double DefaultVpMax { get; set; } = 8000;
        public double DefaultVsMin { get; set; } = 500;
        public double DefaultVsMax { get; set; } = 5000;
        public double DefaultVpVsRatioMin { get; set; } = 1.4;
        public double DefaultVpVsRatioMax { get; set; } = 2.5;

        // 脆性指数计算参数
        public double BrittlenessEMin { get; set; } = 10.0;
        public double BrittlenessEMax { get; set; } = 80.0;
        public double BrittlenessVMin { get; set; } = 0.15;
        public double BrittlenessVMax { get; set; } = 0.35;

        private AppConfig()
        {
            _configFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "config.json");
            LoadConfiguration();
        }

        /// <summary>
        /// 获取配置实例
        /// </summary>
        public static AppConfig Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new AppConfig();
                        }
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// 加载配置
        /// </summary>
        private void LoadConfiguration()
        {
            try
            {
                if (File.Exists(_configFilePath))
                {
                    string json = File.ReadAllText(_configFilePath);
                    var config = JsonConvert.DeserializeObject<AppConfig>(json);
                    if (config != null)
                    {
                        CopyPropertiesFrom(config);
                    }
                }
                else
                {
                    // 创建默认配置
                    SetDefaultPaths();
                    SaveConfiguration();
                }
            }
            catch (Exception ex)
            {
                // 如果加载失败，使用默认配置
                SetDefaultPaths();
                Console.WriteLine($"加载配置文件失败，使用默认配置: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置默认路径
        /// </summary>
        private void SetDefaultPaths()
        {
            string documentsPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
            string appFolder = Path.Combine(documentsPath, ApplicationName);
            
            DefaultExportPath = Path.Combine(appFolder, "导出数据");
            DefaultImportPath = Path.Combine(appFolder, "导入数据");

            // 创建目录
            try
            {
                Directory.CreateDirectory(DefaultExportPath);
                Directory.CreateDirectory(DefaultImportPath);
            }
            catch
            {
                // 如果创建失败，使用当前目录
                DefaultExportPath = AppDomain.CurrentDomain.BaseDirectory;
                DefaultImportPath = AppDomain.CurrentDomain.BaseDirectory;
            }
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        public void SaveConfiguration()
        {
            try
            {
                string json = JsonConvert.SerializeObject(this, Formatting.Indented);
                File.WriteAllText(_configFilePath, json);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"保存配置文件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 从另一个配置对象复制属性
        /// </summary>
        /// <param name="source"></param>
        private void CopyPropertiesFrom(AppConfig source)
        {
            ApplicationName = source.ApplicationName;
            Version = source.Version;
            DatabaseConnectionString = source.DatabaseConnectionString;
            EnableLogging = source.EnableLogging;
            LogRetentionDays = source.LogRetentionDays;
            DefaultExportPath = source.DefaultExportPath;
            DefaultImportPath = source.DefaultImportPath;
            AutoSaveEnabled = source.AutoSaveEnabled;
            AutoSaveIntervalMinutes = source.AutoSaveIntervalMinutes;
            Theme = source.Theme;
            Language = source.Language;
            
            DefaultDensityMin = source.DefaultDensityMin;
            DefaultDensityMax = source.DefaultDensityMax;
            DefaultVpMin = source.DefaultVpMin;
            DefaultVpMax = source.DefaultVpMax;
            DefaultVsMin = source.DefaultVsMin;
            DefaultVsMax = source.DefaultVsMax;
            DefaultVpVsRatioMin = source.DefaultVpVsRatioMin;
            DefaultVpVsRatioMax = source.DefaultVpVsRatioMax;
            
            BrittlenessEMin = source.BrittlenessEMin;
            BrittlenessEMax = source.BrittlenessEMax;
            BrittlenessVMin = source.BrittlenessVMin;
            BrittlenessVMax = source.BrittlenessVMax;
        }

        /// <summary>
        /// 重置为默认配置
        /// </summary>
        public void ResetToDefault()
        {
            ApplicationName = "增强版静态岩石力学参数分析系统";
            Version = "1.0.0";
            DatabaseConnectionString = "";
            EnableLogging = true;
            LogRetentionDays = 30;
            AutoSaveEnabled = true;
            AutoSaveIntervalMinutes = 5;
            Theme = "Dark";
            Language = "zh-CN";
            
            DefaultDensityMin = 1.5;
            DefaultDensityMax = 5.0;
            DefaultVpMin = 1000;
            DefaultVpMax = 8000;
            DefaultVsMin = 500;
            DefaultVsMax = 5000;
            DefaultVpVsRatioMin = 1.4;
            DefaultVpVsRatioMax = 2.5;
            
            BrittlenessEMin = 10.0;
            BrittlenessEMax = 80.0;
            BrittlenessVMin = 0.15;
            BrittlenessVMax = 0.35;
            
            SetDefaultPaths();
            SaveConfiguration();
        }

        /// <summary>
        /// 验证配置有效性
        /// </summary>
        /// <returns></returns>
        public (bool IsValid, string ErrorMessage) ValidateConfiguration()
        {
            if (LogRetentionDays < 1 || LogRetentionDays > 365)
                return (false, "日志保留天数必须在1-365之间");
            
            if (AutoSaveIntervalMinutes < 1 || AutoSaveIntervalMinutes > 60)
                return (false, "自动保存间隔必须在1-60分钟之间");
            
            if (DefaultDensityMin >= DefaultDensityMax)
                return (false, "密度最小值必须小于最大值");
            
            if (DefaultVpMin >= DefaultVpMax)
                return (false, "纵波速度最小值必须小于最大值");
            
            if (DefaultVsMin >= DefaultVsMax)
                return (false, "横波速度最小值必须小于最大值");
            
            if (BrittlenessEMin >= BrittlenessEMax)
                return (false, "脆性指数杨氏模量范围设置错误");
            
            if (BrittlenessVMin >= BrittlenessVMax)
                return (false, "脆性指数泊松比范围设置错误");
            
            return (true, "配置验证通过");
        }

        /// <summary>
        /// 获取配置文件路径
        /// </summary>
        /// <returns></returns>
        public string GetConfigFilePath()
        {
            return _configFilePath;
        }
    }
}
