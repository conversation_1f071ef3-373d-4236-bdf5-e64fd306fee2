#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试简单下载功能
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from modules.simple_downloader import SimpleDownloader
from loguru import logger

def test_simple_download():
    """测试简单下载功能"""
    
    # 测试数据
    test_citations = [
        {
            'reference_number': '1',
            'title': 'The spatial distribution and influencing factors of urban green space',
            'authors': '<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>',
            'year': '2021',
            'journal': 'Sustainability'
        },
        {
            'reference_number': '2', 
            'title': 'Social media as a medium to promote local perception',
            'authors': '<PERSON><PERSON>, N. <PERSON>, J. <PERSON>',
            'year': '2022',
            'journal': '未识别'
        }
    ]
    
    # 设置下载目录
    download_dir = str(project_root / "downloads" / "test")
    os.makedirs(download_dir, exist_ok=True)
    
    print("=== 简单文献下载器测试 ===")
    print(f"下载目录: {download_dir}")
    print(f"测试文献数量: {len(test_citations)}")
    print()
    
    # 创建下载器
    downloader = SimpleDownloader(download_dir)
    
    try:
        print("1. 测试打开搜索页面...")
        urls = downloader.open_urls_for_manual_download(test_citations)
        print(f"   已打开 {len(urls)} 个搜索页面")
        
        input("\n按回车键继续测试自动下载...")
        
        print("\n2. 测试自动下载...")
        results = downloader.batch_download_simple(test_citations)
        
        print(f"\n下载结果:")
        print(f"  总计: {results['total']}")
        print(f"  处理: {results['processed']}")
        print(f"  成功: {results['downloaded']}")
        print(f"  失败: {results['failed']}")
        print(f"  跳过: {results['skipped']}")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        print(f"测试失败: {e}")
    
    finally:
        # 清理
        downloader.close()
        print("\n测试完成")

if __name__ == "__main__":
    test_simple_download()
