#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试程序启动
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """主函数"""
    print("🚀 启动文献引用提取与下载系统...")
    
    try:
        # 导入并启动GUI
        from gui.main_window import LiteratureCitationApp
        
        print("✅ 模块导入成功")
        
        app = LiteratureCitationApp()
        print("✅ 应用创建成功")
        
        print("🎉 程序启动成功！")
        app.run()
        
    except Exception as e:
        print(f"❌ 程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")

if __name__ == "__main__":
    main()
