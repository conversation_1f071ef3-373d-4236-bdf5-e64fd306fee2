#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像验证码处理测试脚本
"""

import sys
import os
import logging

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.image_captcha_helper import show_image_captcha_dialog

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def test_image_captcha_helper():
    """测试图像验证码辅助工具"""
    print("🧪 测试图像验证码辅助工具")
    print("=" * 50)
    
    # 测试参数
    test_screenshot = ""  # 可以提供一个测试截图路径
    test_captcha_text = "请选择包含引擎才能工作的物体的图像"
    
    try:
        print("📱 启动图像验证码辅助界面...")
        result = show_image_captcha_dialog(test_screenshot, test_captcha_text)
        
        print(f"✅ 测试完成，用户选择结果: {result}")
        
        if result:
            print("🎉 用户确认完成验证")
        else:
            print("⏭️ 用户选择跳过验证")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        logger.error(f"测试图像验证码辅助工具失败: {e}")

def test_captcha_detection():
    """测试验证码检测功能"""
    print("\n🔍 测试验证码检测功能")
    print("=" * 50)
    
    try:
        from modules.captcha_handler import CaptchaHandler
        
        # 注意：这里需要一个实际的WebDriver实例
        print("⚠️ 此测试需要实际的浏览器实例")
        print("💡 请在实际使用中通过文献下载系统进行测试")
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("💡 请确保所有依赖已正确安装")

def main():
    """主函数"""
    print("🖼️ 图像验证码处理系统测试")
    print("=" * 60)
    
    # 测试1: 图像验证码辅助工具
    test_image_captcha_helper()
    
    # 测试2: 验证码检测功能
    test_captcha_detection()
    
    print("\n" + "=" * 60)
    print("🏁 测试完成")
    
    # 提供使用说明
    print("\n📖 使用说明:")
    print("1. 图像验证码辅助工具提供友好的用户界面")
    print("2. 自动截图并显示验证码预览")
    print("3. 提供详细的操作指导")
    print("4. 支持验证完成确认和跳过选项")
    print("\n💡 集成到文献下载系统:")
    print("- 验证码处理器会自动识别图像验证码")
    print("- 弹出辅助界面帮助用户完成验证")
    print("- 等待用户确认后继续下载流程")

if __name__ == "__main__":
    main()
