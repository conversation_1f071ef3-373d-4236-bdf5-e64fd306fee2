# 简单文献下载器使用说明

## 概述

简单文献下载器提供了两种下载方式：

1. **简单自动下载** - 完全自动化的下载过程
2. **打开搜索页面** - 为您打开搜索页面，手动下载

## 使用前准备

### 1. 安装依赖

```bash
pip install selenium
```

### 2. 安装Chrome浏览器驱动

- 下载与您的Chrome版本匹配的ChromeDriver
- 将chromedriver.exe放在系统PATH中或项目目录下

### 3. 设置浏览器路径（推荐）

- 在程序的"设置"页面中
- 勾选"使用自定义浏览器路径"
- 点击"浏览"选择您的Chrome浏览器exe文件
- 这样启动会更快更稳定

### 4. 登录Web of Science

- 在浏览器中登录您的校园网账号
- 访问Web of Science确保可以正常使用

## 使用方法

### 方法一：简单自动下载

1. **选择文献**：在文献列表中勾选要下载的文献
2. **设置下载目录**：选择EndNote文件的保存位置
3. **点击"简单自动下载"**
4. **确认下载**：系统会显示下载信息，点击"是"开始下载

**自动下载过程**：

- 自动打开Web of Science网站
- 根据文献标题或作者搜索
- 点击搜索结果进入详情页
- 自动点击"导出"按钮
- 选择"EndNote Desktop"格式下载

### 方法二：打开搜索页面（推荐）

1. **选择文献**：在文献列表中勾选要搜索的文献
2. **点击"打开搜索页面"**
3. **手动下载**：
   - 系统会为每个文献打开一个搜索页面
   - 您可以手动验证搜索结果
   - 点击正确的文献进入详情页
   - 点击"导出" → "EndNote Desktop"下载

## 优势特点

### 简单直接

- 不需要复杂的配置
- 直接操作浏览器，就像手动操作一样
- 利用您已有的校园网登录状态

### 智能匹配

- 优先使用文献标题搜索（更准确）
- 标题不足时使用作者搜索
- 自动跳过信息不足的文献

### 安全可靠

- 使用您的浏览器和登录状态
- 不需要存储账号密码
- 可以随时中断和恢复

## 注意事项

1. **网络连接**：确保校园网连接稳定
2. **浏览器状态**：保持Web of Science登录状态
3. **下载限制**：注意数据库的下载限制
4. **文件管理**：下载的.enw文件会保存在指定目录

## 故障排除

### 问题1：无法连接浏览器

**解决方案**：

- 确保Chrome浏览器已安装
- 检查ChromeDriver版本是否匹配
- 重启程序

### 问题2：搜索结果不准确

**解决方案**：

- 使用"打开搜索页面"方式手动验证
- 检查文献信息是否完整
- 手动修正搜索关键词

### 问题3：下载失败

**解决方案**：

- 检查Web of Science登录状态
- 确认下载目录有写入权限
- 检查网络连接

### 问题4：找不到导出按钮

**解决方案**：

- Web of Science界面可能有更新
- 使用"打开搜索页面"方式手动操作
- 联系技术支持

## 技术支持

如果遇到问题，请：

1. 查看程序日志信息
2. 尝试手动操作验证
3. 检查网络和登录状态
4. 联系开发者

## 更新日志

### v1.0

- 实现基本的自动下载功能
- 支持标题和作者搜索
- 添加手动搜索页面打开功能
