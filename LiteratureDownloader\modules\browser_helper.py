#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器辅助模块
直接在用户的浏览器中打开搜索页面，保持校园网登录状态
"""

import webbrowser
import urllib.parse
from typing import List, Dict
from loguru import logger


class BrowserHelper:
    """浏览器辅助类"""
    
    def __init__(self):
        self.wos_base_url = "https://webofscience.clarivate.cn/wos/alldb/smart-search"
    
    def open_wos_search(self, query: str):
        """在浏览器中打开Web of Science搜索"""
        try:
            # 构建搜索URL
            encoded_query = urllib.parse.quote(query)
            search_url = f"{self.wos_base_url}?query={encoded_query}"
            
            logger.info(f"在浏览器中打开WOS搜索: {query}")
            webbrowser.open(search_url)
            
            return True
            
        except Exception as e:
            logger.error(f"打开浏览器搜索失败: {e}")
            return False
    
    def open_multiple_searches(self, citations: List[Dict[str, str]]):
        """为多个文献打开搜索页面"""
        opened_count = 0
        
        for citation in citations:
            # 构建搜索查询
            query_parts = []
            
            # 优先使用标题
            title = citation.get('title', '').strip()
            if title and title != '未识别' and len(title) > 10:
                query_parts.append(f'TI="{title}"')
            
            # 如果没有标题，使用作者
            elif citation.get('authors'):
                authors = citation.get('authors', '')
                if isinstance(authors, list):
                    authors = ', '.join(authors[:2])
                if authors and authors != '未识别':
                    query_parts.append(f'AU="{authors}"')
            
            # 添加年份
            year = citation.get('year', '').strip()
            if year and year != '未识别' and year.isdigit():
                query_parts.append(f'PY={year}')
            
            if query_parts:
                query = ' AND '.join(query_parts)
                if self.open_wos_search(query):
                    opened_count += 1
        
        logger.info(f"已为 {opened_count} 个文献打开搜索页面")
        return opened_count
    
    def create_search_urls(self, citations: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """为文献创建搜索URL列表"""
        search_urls = []
        
        for i, citation in enumerate(citations):
            ref_num = citation.get('reference_number', str(i+1))
            
            # 构建搜索查询
            query_parts = []
            
            # 优先使用标题
            title = citation.get('title', '').strip()
            if title and title != '未识别' and len(title) > 10:
                query_parts.append(f'TI="{title}"')
                search_type = "标题搜索"
            
            # 如果没有标题，使用作者
            elif citation.get('authors'):
                authors = citation.get('authors', '')
                if isinstance(authors, list):
                    authors = ', '.join(authors[:2])
                if authors and authors != '未识别':
                    query_parts.append(f'AU="{authors}"')
                    search_type = "作者搜索"
            else:
                continue  # 跳过没有足够信息的文献
            
            # 添加年份
            year = citation.get('year', '').strip()
            if year and year != '未识别' and year.isdigit():
                query_parts.append(f'PY={year}')
            
            if query_parts:
                query = ' AND '.join(query_parts)
                encoded_query = urllib.parse.quote(query)
                search_url = f"{self.wos_base_url}?query={encoded_query}"
                
                search_urls.append({
                    'ref_num': ref_num,
                    'title': title[:50] + '...' if len(title) > 50 else title,
                    'authors': citation.get('authors', '未知'),
                    'year': year,
                    'search_type': search_type,
                    'query': query,
                    'url': search_url
                })
        
        return search_urls
    
    def open_google_scholar_search(self, query: str):
        """在Google Scholar中搜索"""
        try:
            encoded_query = urllib.parse.quote(query)
            scholar_url = f"https://scholar.google.com/scholar?q={encoded_query}"
            
            logger.info(f"在Google Scholar中搜索: {query}")
            webbrowser.open(scholar_url)
            
            return True
            
        except Exception as e:
            logger.error(f"打开Google Scholar搜索失败: {e}")
            return False
    
    def open_pubmed_search(self, query: str):
        """在PubMed中搜索"""
        try:
            encoded_query = urllib.parse.quote(query)
            pubmed_url = f"https://pubmed.ncbi.nlm.nih.gov/?term={encoded_query}"
            
            logger.info(f"在PubMed中搜索: {query}")
            webbrowser.open(pubmed_url)
            
            return True
            
        except Exception as e:
            logger.error(f"打开PubMed搜索失败: {e}")
            return False
