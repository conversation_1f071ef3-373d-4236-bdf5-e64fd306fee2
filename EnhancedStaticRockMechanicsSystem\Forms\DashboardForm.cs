using System;
using System.Windows.Forms;
using System.Drawing;

namespace EnhancedStaticRockMechanicsSystem.Forms
{
    public partial class DashboardForm : Form
    {
        private Label lblTitle;
        private Button btnStaticRockMechanics;
        private Button btnExit;
        private string username;

        public DashboardForm(string username)
        {
            this.username = username;
            InitializeComponent();
            LoadData();
        }

        private void LoadData()
        {
            // 简化版本，不需要加载数据
        }

        private void BtnStaticRockMechanics_Click(object sender, EventArgs e)
        {
            try
            {
                // 打开静态岩石力学参数法窗体
                StaticRockMechanicsForm staticRockForm = new StaticRockMechanicsForm();
                this.Hide();
                DialogResult result = staticRockForm.ShowDialog();
                if (result == DialogResult.OK)
                {
                    this.Show();
                }
                else
                {
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"启动静态岩石力学参数法系统时出错：{ex.Message}",
                              "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnExit_Click(object sender, EventArgs e)
        {
            // 退出系统
            Application.Exit();
        }
    }
}
