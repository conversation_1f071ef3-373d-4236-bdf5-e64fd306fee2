﻿using System;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;
using System.IO;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using Newtonsoft.Json;
using System.Collections.Generic;
using EnhancedStaticRockMechanicsSystem.Models;
using EnhancedStaticRockMechanicsSystem.Services;

namespace EnhancedStaticRockMechanicsSystem.Forms
{
    /// <summary>
    /// 增强版静态岩石力学参数法脆性指数计算窗体
    /// </summary>
    public partial class StaticRockMechanicsForm : Form
    {
        #region 字段和属性

        private readonly string username = "";
        private DataTable mechanicsData = new DataTable();
        private DataTable? originalMechanicsData;
        private string? currentExcelFile;

        // 图表缩放相关字段
        private double currentZoom = 1.0;
        private double currentXZoom = 1.0;
        private const double MAX_ZOOM = 15.0;  // Y轴最大放大15倍
        private const double MAX_X_ZOOM = 3.0; // X轴最大放大3倍
        private const double MIN_ZOOM = 1.0;   // 最小不缩小
        private const double ZOOM_FACTOR = 1.2;

        // 数据点和交互相关字段
        private List<RockMechanicsDataPoint> dataPoints = new List<RockMechanicsDataPoint>();
        private HashSet<int> selectedRows = new HashSet<int>();
        private bool showDataPoints = false; // 控制是否显示数据点

        // 列名识别字典
        private readonly Dictionary<string, List<string>> columnPatterns = new Dictionary<string, List<string>>
        {
            ["深度"] = new List<string> { "深度", "depth", "depths", "顶深", "井深", "md", "tvd", "顶深/m", "深度/m", "深度(m)" },
            ["密度"] = new List<string> { "密度", "ρ", "rho", "rhob", "density", "den", "密度/(g/cm³)", "密度(g/cm³)", "岩石密度" },
            ["纵波速度"] = new List<string> { "纵波速度", "vp", "纵波", "p波", "dt", "纵波时差", "纵波速度/(m/s)", "纵波速度(m/s)", "vp(m/s)" },
            ["横波速度"] = new List<string> { "横波速度", "vs", "横波", "s波", "dts", "横波时差", "横波速度/(m/s)", "横波速度(m/s)", "vs(m/s)" }
        };

        #endregion

        #region 构造函数

        public StaticRockMechanicsForm()
        {
            InitializeComponent();
            InitializeForm();
        }

        public StaticRockMechanicsForm(string username)
        {
            this.username = username;
            InitializeComponent();
            InitializeForm();

            if (lblWelcome != null)
            {
                lblWelcome.Text = $"欢迎使用静态岩石力学参数法, {username}";
            }
        }

        #endregion

        #region 初始化方法

        private void InitializeForm()
        {
            // 初始化数据表
            InitializeDataTable();

            // 绑定事件
            Load += StaticRockMechanicsForm_Load;
            Resize += StaticRockMechanicsForm_Resize;
            FormClosing += StaticRockMechanicsForm_FormClosing;

            // 绑定单位选择事件（如果控件存在）
            BindUnitSelectionEvents();
        }

        private void InitializeDataTable()
        {
            mechanicsData = new DataTable();
            mechanicsData.Columns.Add("顶深/m", typeof(double));
            mechanicsData.Columns.Add("底深/m", typeof(double));
            mechanicsData.Columns.Add("密度/(g/cm³)", typeof(double));
            mechanicsData.Columns.Add("纵波速度/(m/s)", typeof(double));
            mechanicsData.Columns.Add("横波速度/(m/s)", typeof(double));
            mechanicsData.Columns.Add("动态杨氏模量/GPa", typeof(double));
            mechanicsData.Columns.Add("动态泊松比", typeof(double));
            mechanicsData.Columns.Add("静态杨氏模量/GPa", typeof(double));
            mechanicsData.Columns.Add("静态泊松比", typeof(double));
            mechanicsData.Columns.Add("脆性指数/%", typeof(double));

            if (dgvMechanicsData != null)
            {
                dgvMechanicsData.DataSource = mechanicsData;
            }
        }

        /// <summary>
        /// 绑定单位选择控件的事件处理器
        /// </summary>
        private void BindUnitSelectionEvents()
        {
            try
            {
                // 查找并绑定密度单位选择控件
                var rbDensityRho = FindControlByName("rbDensityRho") as RadioButton;
                var rbDensityRhob = FindControlByName("rbDensityRhob") as RadioButton;

                // 查找并绑定纵波单位选择控件
                var rbVelocityVp = FindControlByName("rbVelocityVp") as RadioButton;
                var rbVelocityDt = FindControlByName("rbVelocityDt") as RadioButton;

                // 查找并绑定横波单位选择控件
                var rbVelocityVs = FindControlByName("rbVelocityVs") as RadioButton;
                var rbVelocityDts = FindControlByName("rbVelocityDts") as RadioButton;

                // 设置默认选择：DT和DTS（因为这样计算出的脆性指数更准确）
                if (rbVelocityDt != null) rbVelocityDt.Checked = true;
                if (rbVelocityDts != null) rbVelocityDts.Checked = true;
                if (rbDensityRho != null) rbDensityRho.Checked = true; // 默认选择ρ

                // 绑定事件处理器
                if (rbDensityRho != null) rbDensityRho.CheckedChanged += UnitSelection_CheckedChanged;
                if (rbDensityRhob != null) rbDensityRhob.CheckedChanged += UnitSelection_CheckedChanged;
                if (rbVelocityVp != null) rbVelocityVp.CheckedChanged += UnitSelection_CheckedChanged;
                if (rbVelocityDt != null) rbVelocityDt.CheckedChanged += UnitSelection_CheckedChanged;
                if (rbVelocityVs != null) rbVelocityVs.CheckedChanged += UnitSelection_CheckedChanged;
                if (rbVelocityDts != null) rbVelocityDts.CheckedChanged += UnitSelection_CheckedChanged;
            }
            catch (Exception ex)
            {
                // 如果控件不存在，忽略错误（设计器中可能还未创建）
                System.Diagnostics.Debug.WriteLine($"绑定单位选择事件时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 递归查找控件
        /// </summary>
        private Control? FindControlByName(string name)
        {
            return FindControlByName(this, name);
        }

        /// <summary>
        /// 递归查找指定名称的控件
        /// </summary>
        private Control? FindControlByName(Control parent, string name)
        {
            if (parent.Name == name)
                return parent;

            foreach (Control child in parent.Controls)
            {
                var found = FindControlByName(child, name);
                if (found != null)
                    return found;
            }
            return null;
        }

        #endregion




        #region 事件处理方法

        private void StaticRockMechanicsForm_Load(object sender, EventArgs e)
        {
            // 窗体加载时的初始化
            UpdateParameterLabels();
        }

        private void StaticRockMechanicsForm_Resize(object sender, EventArgs e)
        {
            // 窗体大小改变时调整控件位置
            // 这里可以添加响应式布局代码，或者在设计器中使用Anchor和Dock属性
        }

        private void StaticRockMechanicsForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                if (chartBrittleness != null)
                {
                    chartBrittleness.Series.Clear();
                    chartBrittleness.ChartAreas.Clear();
                    chartBrittleness.Legends.Clear();
                    chartBrittleness.Dispose();
                }
                e.Cancel = false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"关闭窗体时出错: {ex.Message}");
                e.Cancel = false;
            }
        }

        private void BtnBack_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void BtnLogout_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void BtnEmergencyExit_Click(object sender, EventArgs e)
        {
            try
            {
                // 检查是否有图表数据可以保存
                if (chartBrittleness.Series.Count == 0)
                {
                    MessageBox.Show("没有图表数据可以保存为对比图！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 保存当前图表数据到全局存储
                SaveChartDataForComparison();

                MessageBox.Show("图表数据已保存，可以在其他系统中查看对比图！", "保存成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存对比图数据时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 保存图表数据用于对比
        /// </summary>
        private void SaveChartDataForComparison()
        {
            try
            {
                // 从图表中提取数据点，确保坐标轴正确
                var chartDataPoints = new List<object>();

                if (chartBrittleness.Series.Count > 0)
                {
                    var series = chartBrittleness.Series[0];
                    foreach (var point in series.Points)
                    {
                        double brittleIndex = point.XValue; // X轴是脆性指数
                        double depth = point.YValues[0];    // Y轴是深度

                        // 确保脆性指数在0-100范围内，深度大于0
                        if (brittleIndex >= 0 && brittleIndex <= 100 && depth > 0)
                        {
                            chartDataPoints.Add(new
                            {
                                TopDepth = depth,
                                BottomDepth = depth,
                                BrittleIndex = brittleIndex
                            });
                        }
                    }
                }

                // 创建对比数据结构
                var comparisonData = new
                {
                    SystemName = "静态岩石力学参数法",
                    DataPoints = chartDataPoints,
                    SaveTime = DateTime.Now,
                    DataCount = chartDataPoints.Count
                };

                // 将数据序列化为JSON并保存到临时文件
                string jsonData = Newtonsoft.Json.JsonConvert.SerializeObject(comparisonData, Newtonsoft.Json.Formatting.Indented);
                string tempPath = Path.Combine(Path.GetTempPath(), "BritSystem_StaticRockMechanicsData.json");
                File.WriteAllText(tempPath, jsonData);

                System.Diagnostics.Debug.WriteLine($"静态岩石力学参数法图表数据已保存到: {tempPath}, 数据点数量: {chartDataPoints.Count}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存对比图数据时出错: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 查看对比图按钮点击事件
        /// </summary>
        private void BtnViewComparison_Click(object sender, EventArgs e)
        {
            try
            {
                // 打开增强版对比图窗体
                EnhancedComparisonChartForm comparisonForm = new EnhancedComparisonChartForm();
                comparisonForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开对比图时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 单位选择变化事件处理器
        /// </summary>
        private void UnitSelection_CheckedChanged(object sender, EventArgs e)
        {
            if (sender is RadioButton rb && rb.Checked)
            {
                // 更新标签文本以反映当前选择的单位
                UpdateParameterLabels();
            }
        }

        /// <summary>
        /// 更新参数标签文本
        /// </summary>
        private void UpdateParameterLabels()
        {
            try
            {
                // 查找标签控件
                var lblDensity = FindControlByName("lblDensity") as Label;
                var lblVp = FindControlByName("lblVp") as Label;
                var lblVs = FindControlByName("lblVs") as Label;

                // 查找单位选择控件
                var rbDensityRhob = FindControlByName("rbDensityRhob") as RadioButton;
                var rbVelocityDt = FindControlByName("rbVelocityDt") as RadioButton;
                var rbVelocityDts = FindControlByName("rbVelocityDts") as RadioButton;

                // 更新密度标签
                if (lblDensity != null)
                {
                    lblDensity.Text = rbDensityRhob?.Checked == true ? "岩石密度 RHOB (g/cm³):" : "岩石密度 ρ (g/cm³):";
                }

                // 更新纵波标签
                if (lblVp != null)
                {
                    lblVp.Text = rbVelocityDt?.Checked == true ? "纵波时差 DT (μs/m):" : "纵波速度 Vp (m/s):";
                }

                // 更新横波标签
                if (lblVs != null)
                {
                    lblVs.Text = rbVelocityDts?.Checked == true ? "横波时差 DTS (μs/m):" : "横波速度 Vs (m/s):";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新参数标签时出错: {ex.Message}");
            }
        }



        private void BtnCalculate_Click(object sender, EventArgs e)
        {
            try
            {
                // 检查是否有导入的数据需要批量计算
                if (mechanicsData != null && mechanicsData.Rows.Count > 0)
                {
                    // 批量计算模式
                    CalculateBatchData();
                }
                else
                {
                    // 单个计算模式
                    CalculateSingleData();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"计算失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 单个数据计算
        /// </summary>
        private void CalculateSingleData()
        {
            // 获取输入参数
            if (!double.TryParse(txtDensity.Text, out double inputDensity) ||
                !double.TryParse(txtVp.Text, out double inputVp) ||
                !double.TryParse(txtVs.Text, out double inputVs))
            {
                MessageBox.Show("请输入有效的数值", "输入错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            // 进行单位转换，统一为标准单位
            double density = ConvertDensityToStandard(inputDensity);
            double vp = ConvertVelocityToStandard(inputVp, true); // true表示纵波
            double vs = ConvertVelocityToStandard(inputVs, false); // false表示横波

            // 根据修正后的公式计算静态岩石力学参数
            var result = CalculateStaticRockMechanics(density, vp, vs);

            // 对于单个计算，使用固定范围计算脆性指数
            double EsMin = 5.0;   // GPa
            double EsMax = 80.0;  // GPa
            double MuSMin = 0.1;
            double MuSMax = 0.4;
            result.BrittlenessIndex = CalculateBrittlenessIndex(result.Es, result.MuS, EsMin, EsMax, MuSMin, MuSMax);

            // 显示计算结果
            lblCalculationResult.Text = $"Ed={result.Ed:F3}GPa, μd={result.MuD:F4}, Es={result.Es:F3}GPa, μs={result.MuS:F4}, BRIT={result.BrittlenessIndex:F2}%";

            // 添加到数据表
            DataRow newRow = mechanicsData.NewRow();
            newRow["顶深/m"] = 0.0; // 默认值，用户可以在导入数据时修改
            newRow["底深/m"] = 0.0; // 默认值
            newRow["密度/(g/cm³)"] = density;
            newRow["纵波速度/(m/s)"] = vp;
            newRow["横波速度/(m/s)"] = vs;
            newRow["动态杨氏模量/GPa"] = result.Ed;
            newRow["动态泊松比"] = result.MuD;
            newRow["静态杨氏模量/GPa"] = result.Es;
            newRow["静态泊松比"] = result.MuS;
            newRow["脆性指数/%"] = result.BrittlenessIndex;

            mechanicsData.Rows.Add(newRow);

            // 检查数据是否异常
            string warningMessage = "";
            if (vp <= Math.Sqrt(2) * vs)
            {
                warningMessage = "\n注意：输入数据可能异常（Vp ≤ √2·Vs），请检查数据准确性。";
            }

            MessageBox.Show($"计算成功！脆性指数为: {result.BrittlenessIndex:F2}%{warningMessage}", "计算结果", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 批量数据计算
        /// </summary>
        private void CalculateBatchData()
        {
            // 智能识别列名
            string? depthColumnName = FindColumnByPattern(mechanicsData, "深度");
            string? densityColumnName = FindColumnByPattern(mechanicsData, "密度");
            string? vpColumnName = FindColumnByPattern(mechanicsData, "纵波速度");
            string? vsColumnName = FindColumnByPattern(mechanicsData, "横波速度");

            // 检查必需的列是否存在
            if (string.IsNullOrEmpty(depthColumnName))
            {
                MessageBox.Show("未找到深度列，请检查数据格式！支持的深度列名包括：深度、depth、顶深、井深等", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            if (string.IsNullOrEmpty(densityColumnName))
            {
                MessageBox.Show("未找到密度列，请检查数据格式！支持的密度列名包括：密度、ρ、rho、rhob等", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            if (string.IsNullOrEmpty(vpColumnName))
            {
                MessageBox.Show("未找到纵波速度列，请检查数据格式！支持的纵波速度列名包括：纵波速度、vp、dt等", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            if (string.IsNullOrEmpty(vsColumnName))
            {
                MessageBox.Show("未找到横波速度列，请检查数据格式！支持的横波速度列名包括：横波速度、vs、dts等", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            // 添加计算结果列（如果不存在）
            if (!mechanicsData.Columns.Contains("动态杨氏模量/GPa"))
                mechanicsData.Columns.Add("动态杨氏模量/GPa", typeof(double));
            if (!mechanicsData.Columns.Contains("动态泊松比"))
                mechanicsData.Columns.Add("动态泊松比", typeof(double));
            if (!mechanicsData.Columns.Contains("静态杨氏模量/GPa"))
                mechanicsData.Columns.Add("静态杨氏模量/GPa", typeof(double));
            if (!mechanicsData.Columns.Contains("静态泊松比"))
                mechanicsData.Columns.Add("静态泊松比", typeof(double));
            if (!mechanicsData.Columns.Contains("脆性指数/%"))
                mechanicsData.Columns.Add("脆性指数/%", typeof(double));

            int calculatedCount = 0;
            int errorCount = 0;

            // 第一遍：计算所有静态参数并收集范围
            List<double> allEs = new List<double>();
            List<double> allMuS = new List<double>();
            List<RockMechanicsResult> results = new List<RockMechanicsResult>();

            foreach (DataRow row in mechanicsData.Rows)
            {
                try
                {
                    // 获取数据
                    if (row[densityColumnName] == DBNull.Value || row[vpColumnName] == DBNull.Value || row[vsColumnName] == DBNull.Value)
                    {
                        results.Add(new RockMechanicsResult()); // 添加空结果
                        continue;
                    }

                    if (!double.TryParse(row[densityColumnName].ToString(), out double density) ||
                        !double.TryParse(row[vpColumnName].ToString(), out double vp) ||
                        !double.TryParse(row[vsColumnName].ToString(), out double vs))
                    {
                        results.Add(new RockMechanicsResult()); // 添加空结果
                        errorCount++;
                        continue;
                    }

                    // 进行单位转换
                    density = ConvertDensityToStandard(density);
                    vp = ConvertVelocityToStandard(vp, true);
                    vs = ConvertVelocityToStandard(vs, false);

                    // 计算岩石力学参数
                    var result = CalculateStaticRockMechanics(density, vp, vs);
                    results.Add(result);

                    // 收集静态参数用于计算范围
                    allEs.Add(result.Es);
                    allMuS.Add(result.MuS);

                    calculatedCount++;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"计算行数据时出错: {ex.Message}");
                    results.Add(new RockMechanicsResult()); // 添加空结果
                    errorCount++;
                }
            }

            // 计算动态范围
            double EsMin = allEs.Count > 0 ? allEs.Min() : 0;
            double EsMax = allEs.Count > 0 ? allEs.Max() : 100;
            double MuSMin = allMuS.Count > 0 ? allMuS.Min() : 0;
            double MuSMax = allMuS.Count > 0 ? allMuS.Max() : 1;

            // 第二遍：使用动态范围计算脆性指数并保存结果
            for (int i = 0; i < mechanicsData.Rows.Count; i++)
            {
                var result = results[i];
                if (result.Es > 0) // 有效结果
                {
                    // 使用动态范围计算脆性指数
                    result.BrittlenessIndex = CalculateBrittlenessIndex(result.Es, result.MuS, EsMin, EsMax, MuSMin, MuSMax);

                    // 保存计算结果
                    mechanicsData.Rows[i]["动态杨氏模量/GPa"] = result.Ed;
                    mechanicsData.Rows[i]["动态泊松比"] = result.MuD;
                    mechanicsData.Rows[i]["静态杨氏模量/GPa"] = result.Es;
                    mechanicsData.Rows[i]["静态泊松比"] = result.MuS;
                    mechanicsData.Rows[i]["脆性指数/%"] = result.BrittlenessIndex;
                }
            }

            // 刷新数据显示
            dgvMechanicsData.Refresh();

            MessageBox.Show($"批量计算完成！成功计算 {calculatedCount} 行数据，{errorCount} 行数据计算失败。\n" +
                          $"静态杨氏模量范围: {EsMin:F2} - {EsMax:F2} GPa\n" +
                          $"静态泊松比范围: {MuSMin:F4} - {MuSMax:F4}", "计算结果", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 将密度转换为标准单位 (g/cm³)
        /// </summary>
        private double ConvertDensityToStandard(double inputValue)
        {
            // ρ 和 RHOB 都是 g/cm³，无需转换
            return inputValue;
        }

        /// <summary>
        /// 将速度/时差转换为标准速度单位 (m/s)
        /// </summary>
        /// <param name="inputValue">输入值</param>
        /// <param name="isVp">是否为纵波，true=纵波，false=横波</param>
        /// <returns>转换后的速度值 (m/s)</returns>
        private double ConvertVelocityToStandard(double inputValue, bool isVp)
        {
            try
            {
                if (isVp)
                {
                    // 纵波：检查是否选择了DT
                    var rbVelocityDt = FindControlByName("rbVelocityDt") as RadioButton;
                    if (rbVelocityDt?.Checked == true)
                    {
                        // DT (μs/m) 转换为 Vp (m/s)
                        // Vp = 10^6 / DT
                        return 1000000.0 / inputValue;
                    }
                    else
                    {
                        // 已经是 Vp (m/s)，直接返回
                        return inputValue;
                    }
                }
                else
                {
                    // 横波：检查是否选择了DTS
                    var rbVelocityDts = FindControlByName("rbVelocityDts") as RadioButton;
                    if (rbVelocityDts?.Checked == true)
                    {
                        // DTS (μs/m) 转换为 Vs (m/s)
                        // Vs = 10^6 / DTS
                        return 1000000.0 / inputValue;
                    }
                    else
                    {
                        // 已经是 Vs (m/s)，直接返回
                        return inputValue;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"单位转换时出错: {ex.Message}");
                // 出错时直接返回输入值
                return inputValue;
            }
        }
        private void BtnImport_Click(object sender, EventArgs e)
        {
            using (var ofd = new OpenFileDialog { Filter = "Excel文件|*.xls;*.xlsx" })
            {
                if (ofd.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        currentExcelFile = ofd.FileName;
                        var ds = ReadExcelSheets(currentExcelFile);
                        if (ds != null && ds.Tables.Count > 0)
                        {
                            mechanicsData = ds.Tables[0];
                            originalMechanicsData = mechanicsData.Copy();
                            dgvMechanicsData.DataSource = mechanicsData;

                            MessageBox.Show("文件加载成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            MessageBox.Show("文件内容为空！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"文件读取失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void BtnExport_Click(object sender, EventArgs e)
        {
            try
            {
                if (mechanicsData == null || mechanicsData.Rows.Count == 0)
                {
                    MessageBox.Show("没有数据可以导出！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                SaveFileDialog saveFileDialog = new SaveFileDialog();
                saveFileDialog.Filter = "Excel文件 (*.xlsx)|*.xlsx";
                saveFileDialog.Title = "保存Excel文件";
                saveFileDialog.FileName = $"静态岩石力学参数数据_{DateTime.Now:yyyyMMdd}.xlsx";

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    ExportExcelFile(saveFileDialog.FileName);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导出数据时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 智能识别列名
        /// </summary>
        private string? FindColumnByPattern(DataTable dataTable, string patternKey)
        {
            if (!columnPatterns.ContainsKey(patternKey))
                return null;

            var patterns = columnPatterns[patternKey];

            foreach (DataColumn column in dataTable.Columns)
            {
                string columnName = column.ColumnName.Trim().ToLower();
                foreach (string pattern in patterns)
                {
                    if (columnName.Contains(pattern.ToLower()))
                    {
                        return column.ColumnName;
                    }
                }
            }
            return null;
        }

        /// <summary>
        /// 验证数值是否适合Chart控件显示，避免溢出
        /// </summary>
        private static bool IsValidChartValue(double value)
        {
            return !double.IsNaN(value) &&
                   !double.IsInfinity(value) &&
                   value >= -1e10 &&
                   value <= 1e10;
        }

        private void BtnGenerateCurve_Click(object sender, EventArgs e)
        {
            try
            {
                if (mechanicsData == null || mechanicsData.Rows.Count == 0)
                {
                    MessageBox.Show("没有数据可以生成曲线！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 清除现有系列和数据点
                chartBrittleness.Series.Clear();
                dataPoints.Clear();

                // 智能识别列名
                string? depthColumnName = FindColumnByPattern(mechanicsData, "深度");
                string? brittlenessColumnName = "脆性指数/%"; // 这是计算后的列名

                // 检查是否找到深度列
                if (string.IsNullOrEmpty(depthColumnName))
                {
                    MessageBox.Show("未找到深度列，请检查数据格式！支持的深度列名包括：深度、depth、顶深、井深等", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 检查是否存在脆性指数列
                if (!mechanicsData.Columns.Contains(brittlenessColumnName))
                {
                    MessageBox.Show("未找到脆性指数列，请先进行计算！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 创建脆性指数曲线系列 - 只显示曲线，不显示数据点
                var series = new Series("脆性指数")
                {
                    ChartType = SeriesChartType.Spline, // 使用平滑曲线
                    Color = Color.Cyan,
                    BorderWidth = 2,
                    MarkerStyle = MarkerStyle.None, // 默认不显示数据点
                    MarkerSize = 0
                };

                // 创建高亮点系列（用于显示选中的点）
                var highlightSeries = new Series("高亮点")
                {
                    ChartType = SeriesChartType.Point,
                    Color = Color.Red,
                    MarkerStyle = MarkerStyle.Circle,
                    MarkerSize = 8,
                    MarkerBorderColor = Color.Yellow,
                    MarkerBorderWidth = 2
                };

                // 添加数据点并验证数值范围
                List<double> validDepths = new List<double>();
                List<double> validBrittleness = new List<double>();
                int rowIndex = 0;

                foreach (DataRow row in mechanicsData.Rows)
                {
                    if (row[brittlenessColumnName] != DBNull.Value && row[depthColumnName] != DBNull.Value)
                    {
                        if (double.TryParse(row[brittlenessColumnName].ToString(), out double brittleness) &&
                            double.TryParse(row[depthColumnName].ToString(), out double depth))
                        {
                            // 验证数值范围，避免溢出
                            if (IsValidChartValue(brittleness) && IsValidChartValue(depth) &&
                                brittleness >= 0 && brittleness <= 100 && depth > 0)
                            {
                                series.Points.AddXY(brittleness, depth);
                                validDepths.Add(depth);
                                validBrittleness.Add(brittleness);

                                // 添加到数据点列表
                                dataPoints.Add(new RockMechanicsDataPoint
                                {
                                    Depth = depth,
                                    BrittlenessIndex = brittleness,
                                    RowIndex = rowIndex
                                });
                            }
                        }
                    }
                    rowIndex++;
                }

                if (series.Points.Count == 0)
                {
                    MessageBox.Show("没有有效的数据点可以生成曲线！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                chartBrittleness.Series.Add(series);
                chartBrittleness.Series.Add(highlightSeries);

                // 设置图表区域属性
                var chartArea = chartBrittleness.ChartAreas[0];

                // 启用缩放和滚动功能
                chartArea.CursorX.IsUserEnabled = true;
                chartArea.CursorX.IsUserSelectionEnabled = true;
                chartArea.CursorY.IsUserEnabled = true;
                chartArea.CursorY.IsUserSelectionEnabled = true;

                chartArea.AxisX.ScaleView.Zoomable = true;
                chartArea.AxisY.ScaleView.Zoomable = true;
                chartArea.AxisX.ScrollBar.IsPositionedInside = true;
                chartArea.AxisY.ScrollBar.IsPositionedInside = true;

                // 设置Y轴（深度）反向显示
                chartArea.AxisY.IsReversed = true;

                // 安全设置轴范围，避免溢出
                if (validDepths.Count > 0 && validBrittleness.Count > 0)
                {
                    double minDepth = validDepths.Min();
                    double maxDepth = validDepths.Max();
                    double minBrittleness = validBrittleness.Min();
                    double maxBrittleness = validBrittleness.Max();

                    // 确保范围合理
                    chartArea.AxisY.Minimum = Math.Max(0, minDepth - (maxDepth - minDepth) * 0.1);
                    chartArea.AxisY.Maximum = maxDepth + (maxDepth - minDepth) * 0.1;
                    chartArea.AxisX.Minimum = Math.Max(0, minBrittleness - 5);
                    chartArea.AxisX.Maximum = Math.Min(100, maxBrittleness + 5);

                    // 设置初始缩放视图（显示部分数据）
                    double visibleRange = (maxDepth - minDepth) * 0.4; // 显示40%的数据范围
                    double midPoint = (minDepth + maxDepth) / 2;
                    double viewMin = midPoint - visibleRange / 2;
                    double viewMax = midPoint + visibleRange / 2;

                    // 确保视图范围在数据范围内
                    viewMin = Math.Max(viewMin, minDepth);
                    viewMax = Math.Min(viewMax, maxDepth);

                    // 设置初始视图范围
                    chartArea.AxisY.ScaleView.Zoom(viewMin, viewMax);
                    currentZoom = (maxDepth - minDepth) / (viewMax - viewMin);
                }

                // 设置轴标题
                chartArea.AxisX.Title = "脆性指数 (%)";
                chartArea.AxisY.Title = "深度 (m)";

                // 设置网格线
                chartArea.AxisX.MajorGrid.Enabled = true;
                chartArea.AxisY.MajorGrid.Enabled = true;
                chartArea.AxisX.MajorGrid.LineColor = Color.LightGray;
                chartArea.AxisY.MajorGrid.LineColor = Color.LightGray;

                // 绑定鼠标事件
                chartBrittleness.MouseWheel += Chart_MouseWheel;
                chartBrittleness.MouseClick += Chart_MouseClick;

                // 绑定DataGridView事件
                dgvMechanicsData.CellClick += DataGridView_CellClick;

                MessageBox.Show("曲线生成成功！使用鼠标滚轮可以缩放，点击数据表行可以高亮对应点。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"生成曲线失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }


        private void BtnReset_Click(object sender, EventArgs e)
        {
            try
            {
                // 清空输入框
                txtDensity.Text = "";
                txtVp.Text = "";
                txtVs.Text = "";
                lblCalculationResult.Text = "计算结果将在此显示";

                // 清空图表
                chartBrittleness.Series.Clear();

                // 重置数据表
                if (originalMechanicsData != null)
                {
                    mechanicsData = originalMechanicsData.Copy();
                    dgvMechanicsData.DataSource = mechanicsData;
                }
                else
                {
                    InitializeDataTable();
                }

                MessageBox.Show("已重置所有数据！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"重置失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnSaveCurve_Click(object sender, EventArgs e)
        {
            try
            {
                if (chartBrittleness.Series.Count == 0)
                {
                    MessageBox.Show("没有曲线可以保存！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                SaveFileDialog saveFileDialog = new SaveFileDialog();
                saveFileDialog.Filter = "PNG图片 (*.png)|*.png|JPEG图片 (*.jpg)|*.jpg";
                saveFileDialog.Title = "保存曲线图片";
                saveFileDialog.FileName = $"静态岩石力学参数脆性指数曲线_{DateTime.Now:yyyyMMdd}.png";

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    chartBrittleness.SaveImage(saveFileDialog.FileName, ChartImageFormat.Png);
                    MessageBox.Show("曲线图片保存成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存曲线失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region 图表交互事件

        /// <summary>
        /// 图表鼠标滚轮事件 - 实现缩放功能
        /// </summary>
        private void Chart_MouseWheel(object sender, MouseEventArgs e)
        {
            try
            {
                if (chartBrittleness == null || chartBrittleness.ChartAreas.Count == 0 || dataPoints.Count == 0)
                    return;

                var chartArea = chartBrittleness.ChartAreas[0];
                bool isZoomX = (ModifierKeys & Keys.Shift) == Keys.Shift; // Shift键缩放X轴
                double zoomFactor = e.Delta > 0 ? 1 / ZOOM_FACTOR : ZOOM_FACTOR; // 向上滚动缩小，向下滚动放大

                if (isZoomX)
                {
                    // X轴缩放
                    try
                    {
                        double newXZoom = currentXZoom * (e.Delta > 0 ? ZOOM_FACTOR : 1 / ZOOM_FACTOR);
                        newXZoom = Math.Min(Math.Max(newXZoom, MIN_ZOOM), MAX_X_ZOOM);

                        if (Math.Abs(newXZoom - currentXZoom) < 0.01) return;

                        // 获取鼠标位置对应的X轴值
                        double xValue = chartArea.AxisX.PixelPositionToValue(e.X);

                        // 计算新的显示范围
                        double currentRange = chartArea.AxisX.ScaleView.ViewMaximum - chartArea.AxisX.ScaleView.ViewMinimum;
                        double newRange = currentRange / (newXZoom / currentXZoom);
                        double newMin = xValue - (newRange / 2);
                        double newMax = xValue + (newRange / 2);

                        // 确保不超出数据范围
                        if (newMin < chartArea.AxisX.Minimum)
                        {
                            newMin = chartArea.AxisX.Minimum;
                            newMax = newMin + newRange;
                        }
                        if (newMax > chartArea.AxisX.Maximum)
                        {
                            newMax = chartArea.AxisX.Maximum;
                            newMin = newMax - newRange;
                        }

                        currentXZoom = newXZoom;
                        chartArea.AxisX.ScaleView.Zoom(newMin, newMax);
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"X轴缩放出错: {ex.Message}");
                        chartArea.AxisX.ScaleView.ZoomReset();
                    }
                }
                else
                {
                    // Y轴缩放
                    try
                    {
                        double newZoom = currentZoom * (e.Delta > 0 ? ZOOM_FACTOR : 1 / ZOOM_FACTOR);
                        newZoom = Math.Min(Math.Max(newZoom, MIN_ZOOM), MAX_ZOOM);

                        if (Math.Abs(newZoom - currentZoom) < 0.01) return;

                        // 获取鼠标位置对应的Y轴值
                        double yValue = chartArea.AxisY.PixelPositionToValue(e.Y);

                        // 计算新的显示范围
                        double currentRange = chartArea.AxisY.ScaleView.ViewMaximum - chartArea.AxisY.ScaleView.ViewMinimum;
                        double newRange = currentRange / (newZoom / currentZoom);
                        double newMin = yValue - (newRange / 2);
                        double newMax = yValue + (newRange / 2);

                        // 确保不超出数据范围
                        if (newMin < chartArea.AxisY.Minimum)
                        {
                            newMin = chartArea.AxisY.Minimum;
                            newMax = newMin + newRange;
                        }
                        if (newMax > chartArea.AxisY.Maximum)
                        {
                            newMax = chartArea.AxisY.Maximum;
                            newMin = newMax - newRange;
                        }

                        currentZoom = newZoom;
                        chartArea.AxisY.ScaleView.Zoom(newMin, newMax);
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Y轴缩放出错: {ex.Message}");
                        chartArea.AxisY.ScaleView.ZoomReset();
                    }
                }

                // 强制重绘图表
                chartBrittleness.Invalidate();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"鼠标滚轮事件出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 图表鼠标点击事件
        /// </summary>
        private void Chart_MouseClick(object sender, MouseEventArgs e)
        {
            try
            {
                if (e.Button != MouseButtons.Left || dataPoints.Count == 0)
                    return;

                var chartArea = chartBrittleness.ChartAreas[0];

                // 将鼠标坐标转换为图表坐标
                double xValue = chartArea.AxisX.PixelPositionToValue(e.X);
                double yValue = chartArea.AxisY.PixelPositionToValue(e.Y);

                // 查找最近的数据点
                RockMechanicsDataPoint? nearestPoint = null;
                double minDistance = double.MaxValue;

                foreach (var point in dataPoints)
                {
                    double distance = Math.Sqrt(Math.Pow(point.BrittlenessIndex - xValue, 2) + Math.Pow(point.Depth - yValue, 2));
                    if (distance < minDistance)
                    {
                        minDistance = distance;
                        nearestPoint = point;
                    }
                }

                // 如果找到了足够近的点，高亮显示
                if (nearestPoint.HasValue && minDistance < 10) // 距离阈值
                {
                    selectedRows.Clear();
                    selectedRows.Add(nearestPoint.Value.RowIndex);

                    // 高亮图表中的点
                    HighlightChartPoint(nearestPoint.Value);

                    // 选中数据表中对应的行
                    if (nearestPoint.Value.RowIndex < dgvMechanicsData.Rows.Count)
                    {
                        dgvMechanicsData.ClearSelection();
                        dgvMechanicsData.Rows[nearestPoint.Value.RowIndex].Selected = true;
                        dgvMechanicsData.FirstDisplayedScrollingRowIndex = nearestPoint.Value.RowIndex;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"图表点击事件出错: {ex.Message}");
            }
        }

        /// <summary>
        /// DataGridView单元格点击事件
        /// </summary>
        private void DataGridView_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                if (e.RowIndex >= 0 && e.ColumnIndex >= 0)
                {
                    selectedRows.Clear();
                    selectedRows.Add(e.RowIndex);

                    // 查找对应的数据点
                    var matchingPoint = dataPoints.FirstOrDefault(p => p.RowIndex == e.RowIndex);
                    if (matchingPoint.RowIndex >= 0)
                    {
                        HighlightChartPoint(matchingPoint);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"数据表点击事件出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 高亮显示图表中的特定点
        /// </summary>
        private void HighlightChartPoint(RockMechanicsDataPoint point)
        {
            try
            {
                if (chartBrittleness.Series.Count < 2)
                    return;

                var highlightSeries = chartBrittleness.Series[1]; // 高亮点系列
                highlightSeries.Points.Clear();

                // 添加高亮点
                highlightSeries.Points.AddXY(point.BrittlenessIndex, point.Depth);

                // 强制重绘
                chartBrittleness.Invalidate();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"高亮显示点时出错: {ex.Message}");
            }
        }

        #endregion

        #region 计算方法

        /// <summary>
        /// 计算结果结构体
        /// </summary>
        public struct RockMechanicsResult
        {
            public double Ed;              // 动态杨氏模量 (GPa)
            public double MuD;             // 动态泊松比
            public double Es;              // 静态杨氏模量 (GPa)
            public double MuS;             // 静态泊松比
            public double BrittlenessIndex; // 脆性指数 (%)
        }

        /// <summary>
        /// 岩石力学数据点结构体
        /// </summary>
        public struct RockMechanicsDataPoint
        {
            public double Depth;           // 深度 (m)
            public double BrittlenessIndex; // 脆性指数 (%)
            public int RowIndex;           // 原始数据行索引
        }

        /// <summary>
        /// 根据您提供的公式计算静态岩石力学参数
        /// </summary>
        /// <param name="density">岩石密度 (g/cm³)</param>
        /// <param name="vp">纵波速度 (m/s)</param>
        /// <param name="vs">横波速度 (m/s)</param>
        /// <returns>计算结果</returns>
        private RockMechanicsResult CalculateStaticRockMechanics(double density, double vp, double vs)
        {
            var result = new RockMechanicsResult();

            // 数据验证：检查Vp > √2 * Vs
            if (vp <= Math.Sqrt(2) * vs)
            {
                // 数据异常，但仍继续计算，只是标记异常
                System.Diagnostics.Debug.WriteLine($"警告: Vp({vp}) <= √2·Vs({vs * Math.Sqrt(2):F2})，数据可能异常");
            }

            // 计算动态杨氏模量 Ed (GPa)
            // 修正公式：Ed = ρ * Vs^2 * (3Vp^2 - 4Vs^2) / ((Vp^2 - Vs^2) * 1000)
            double vp2 = vp * vp;
            double vs2 = vs * vs;
            double denominator = vp2 - vs2;

            // 密度单位已经是g/cm³，直接使用，不需要额外转换
            result.Ed = density * vs2 * (3 * vp2 - 4 * vs2) / (denominator * 1000); // 结果单位为GPa

            // 计算动态泊松比 μd
            // μd = (Vp^2 - 2Vs^2) / (2(Vp^2 - Vs^2))
            result.MuD = (vp2 - 2 * vs2) / (2 * denominator);

            // 计算静态杨氏模量 Es (GPa)
            // Es = Ed × 0.5823 + 7.566
            result.Es = result.Ed * 0.5823 + 7.566;

            // 计算静态泊松比 μs
            // μs = μd × 0.6648 + 0.0514
            result.MuS = result.MuD * 0.6648 + 0.0514;

            // 脆性指数计算将在批量计算时使用动态范围
            // 这里先设置为0，在批量计算时会重新计算
            result.BrittlenessIndex = 0;

            return result;
        }

        /// <summary>
        /// 使用动态范围计算脆性指数
        /// </summary>
        /// <param name="Es">静态杨氏模量</param>
        /// <param name="MuS">静态泊松比</param>
        /// <param name="EsMin">数据集中Es的最小值</param>
        /// <param name="EsMax">数据集中Es的最大值</param>
        /// <param name="MuSMin">数据集中MuS的最小值</param>
        /// <param name="MuSMax">数据集中MuS的最大值</param>
        /// <returns>脆性指数</returns>
        private double CalculateBrittlenessIndex(double Es, double MuS, double EsMin, double EsMax, double MuSMin, double MuSMax)
        {
            // 归一化杨氏模量脆性指数
            // EBRIT = (Es - Emin) / (Emax - Emin) × 100%
            double EBRIT = 0;
            if (EsMax > EsMin)
            {
                EBRIT = (Es - EsMin) / (EsMax - EsMin) * 100;
                EBRIT = Math.Max(0, Math.Min(100, EBRIT)); // 限制在0-100%范围内
            }

            // 归一化泊松比脆性指数
            // μBRIT = (μmax - μs) / (μmax - μmin) × 100%
            double MuBRIT = 0;
            if (MuSMax > MuSMin)
            {
                MuBRIT = (MuSMax - MuS) / (MuSMax - MuSMin) * 100;
                MuBRIT = Math.Max(0, Math.Min(100, MuBRIT)); // 限制在0-100%范围内
            }

            // 综合脆性指数
            // BRITe = (EBRIT + μBRIT) / 2
            return (EBRIT + MuBRIT) / 2;
        }

        #endregion
        #region Excel读写方法

        /// <summary>
        /// 读取Excel文件
        /// </summary>
        private DataSet ReadExcelSheets(string filePath)
        {
            try
            {
                DataSet dataSet = new DataSet();

                using (FileStream stream = new FileStream(filePath, FileMode.Open, FileAccess.Read))
                {
                    IWorkbook workbook = null;

                    if (Path.GetExtension(filePath).ToLower() == ".xls")
                    {
                        workbook = new HSSFWorkbook(stream);
                    }
                    else
                    {
                        workbook = new XSSFWorkbook(stream);
                    }

                    for (int i = 0; i < workbook.NumberOfSheets; i++)
                    {
                        ISheet sheet = workbook.GetSheetAt(i);
                        DataTable dataTable = new DataTable(sheet.SheetName);

                        // 读取表头
                        IRow headerRow = sheet.GetRow(0);
                        if (headerRow != null)
                        {
                            for (int j = 0; j < headerRow.LastCellNum; j++)
                            {
                                ICell cell = headerRow.GetCell(j);
                                string columnName = cell?.ToString() ?? $"Column{j + 1}";
                                dataTable.Columns.Add(columnName, typeof(object));
                            }
                        }

                        // 读取数据行
                        for (int rowIndex = 1; rowIndex <= sheet.LastRowNum; rowIndex++)
                        {
                            IRow row = sheet.GetRow(rowIndex);
                            if (row != null)
                            {
                                DataRow dataRow = dataTable.NewRow();
                                for (int cellIndex = 0; cellIndex < dataTable.Columns.Count; cellIndex++)
                                {
                                    ICell cell = row.GetCell(cellIndex);
                                    if (cell != null)
                                    {
                                        switch (cell.CellType)
                                        {
                                            case CellType.Numeric:
                                                dataRow[cellIndex] = cell.NumericCellValue;
                                                break;
                                            case CellType.String:
                                                dataRow[cellIndex] = cell.StringCellValue;
                                                break;
                                            case CellType.Boolean:
                                                dataRow[cellIndex] = cell.BooleanCellValue;
                                                break;
                                            case CellType.Formula:
                                                try
                                                {
                                                    dataRow[cellIndex] = cell.NumericCellValue;
                                                }
                                                catch
                                                {
                                                    dataRow[cellIndex] = cell.StringCellValue;
                                                }
                                                break;
                                            default:
                                                dataRow[cellIndex] = cell.ToString();
                                                break;
                                        }
                                    }
                                }
                                dataTable.Rows.Add(dataRow);
                            }
                        }

                        dataSet.Tables.Add(dataTable);
                    }
                }

                return dataSet;
            }
            catch (Exception ex)
            {
                throw new Exception($"读取Excel文件失败: {ex.Message}");
            }
        }
        /// <summary>
        /// 导出Excel文件
        /// </summary>
        private void ExportExcelFile(string filePath)
        {
            try
            {
                using (FileStream stream = new FileStream(filePath, FileMode.Create, FileAccess.Write))
                {
                    IWorkbook workbook = new XSSFWorkbook();
                    ISheet sheet = workbook.CreateSheet("静态岩石力学参数数据");

                    // 创建表头
                    IRow headerRow = sheet.CreateRow(0);
                    for (int i = 0; i < mechanicsData.Columns.Count; i++)
                    {
                        headerRow.CreateCell(i).SetCellValue(mechanicsData.Columns[i].ColumnName);
                    }

                    // 创建数据行
                    for (int i = 0; i < mechanicsData.Rows.Count; i++)
                    {
                        IRow dataRow = sheet.CreateRow(i + 1);
                        for (int j = 0; j < mechanicsData.Columns.Count; j++)
                        {
                            var cellValue = mechanicsData.Rows[i][j];
                            if (cellValue != DBNull.Value)
                            {
                                if (double.TryParse(cellValue.ToString(), out double numValue))
                                {
                                    dataRow.CreateCell(j).SetCellValue(numValue);
                                }
                                else
                                {
                                    dataRow.CreateCell(j).SetCellValue(cellValue.ToString());
                                }
                            }
                        }
                    }

                    // 自动调整列宽
                    for (int i = 0; i < mechanicsData.Columns.Count; i++)
                    {
                        sheet.AutoSizeColumn(i);
                    }

                    workbook.Write(stream, false);
                }

                MessageBox.Show("数据导出成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                throw new Exception($"导出Excel文件失败: {ex.Message}");
            }
        }

        #endregion
    }
}
