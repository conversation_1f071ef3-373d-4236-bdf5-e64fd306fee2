using System;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using EnhancedStaticRockMechanicsSystem.Services;

namespace EnhancedStaticRockMechanicsSystem.Forms
{
    /// <summary>
    /// 批量导入向导窗体
    /// </summary>
    public partial class BatchImportWizard : Form
    {
        public string[] SelectedFiles => lstFiles.Items.Cast<string>().ToArray();

        public BatchImportWizard()
        {
            InitializeComponent();
            BindEvents();
        }

        private void BindEvents()
        {
            // 绑定设计器中创建的控件事件
            if (btnAddFiles != null)
                btnAddFiles.Click += BtnAddFiles_Click;

            if (btnRemoveFiles != null)
                btnRemoveFiles.Click += BtnRemoveFiles_Click;

            if (btnPreview != null)
                btnPreview.Click += BtnPreview_Click;

            // btnOK和btnCancel的DialogResult已在设计器中设置

        }



        private void BtnAddFiles_Click(object sender, EventArgs e)
        {
            var manager = new UnifiedComparisonDataManager();
            var files = manager.ShowSmartFileDialog();

            foreach (var file in files)
            {
                if (!lstFiles.Items.Contains(file))
                {
                    lstFiles.Items.Add(file);
                }
            }

            UpdateUI();
        }

        private void BtnRemoveFiles_Click(object sender, EventArgs e)
        {
            var selectedItems = lstFiles.SelectedItems.Cast<string>().ToArray();
            foreach (var item in selectedItems)
            {
                lstFiles.Items.Remove(item);
            }

            UpdateUI();
        }

        private async void BtnPreview_Click(object sender, EventArgs e)
        {
            if (lstFiles.Items.Count == 0) return;

            try
            {
                progressBar.Visible = true;
                progressBar.Style = ProgressBarStyle.Marquee;
                btnPreview.Enabled = false;

                var manager = new UnifiedComparisonDataManager();
                var dataSets = await manager.LoadComparisonDataFromMultipleSources(SelectedFiles);

                // 创建预览数据表
                var previewTable = new DataTable();
                previewTable.Columns.Add("系统名称", typeof(string));
                previewTable.Columns.Add("文件名", typeof(string));
                previewTable.Columns.Add("数据点数", typeof(int));
                previewTable.Columns.Add("关联图片数", typeof(int));
                previewTable.Columns.Add("状态", typeof(string));

                foreach (var dataSet in dataSets)
                {
                    previewTable.Rows.Add(
                        dataSet.SystemName,
                        System.IO.Path.GetFileName(dataSet.DataSource),
                        dataSet.DataPoints.Count,
                        dataSet.AssociatedImages.Count,
                        dataSet.IsValid() ? "有效" : "无效"
                    );
                }

                dgvPreview.DataSource = previewTable;

                // 调整列宽
                if (dgvPreview.Columns.Count > 0)
                {
                    dgvPreview.AutoResizeColumns(DataGridViewAutoSizeColumnsMode.AllCells);
                }

                btnOK.Enabled = dataSets.Count > 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"预览失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                progressBar.Visible = false;
                btnPreview.Enabled = true;
            }
        }

        private void UpdateUI()
        {
            btnRemoveFiles.Enabled = lstFiles.SelectedItems.Count > 0;
            btnPreview.Enabled = lstFiles.Items.Count > 0;

            if (lstFiles.Items.Count == 0)
            {
                dgvPreview.DataSource = null;
                btnOK.Enabled = false;
            }
        }

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);
            UpdateUI();
        }
    }
}
