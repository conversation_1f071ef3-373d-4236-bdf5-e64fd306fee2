#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试智能搜索助手
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_smart_search():
    """测试智能搜索助手"""

    # 测试数据
    test_citations = [
        {
            'reference_number': '1',
            'title': 'Machine learning classification algorithms',
            'authors': ['<PERSON>, <PERSON>', '<PERSON>, A.'],
            'year': '2023',
            'journal': 'Nature'
        },
        {
            'reference_number': '2',
            'title': 'Deep learning neural networks',
            'authors': '<PERSON>, <PERSON>, <PERSON>, <PERSON>',
            'year': '2022',
            'journal': 'Science'
        }
    ]

    print("🤖 智能搜索助手测试 - 全自动版本")
    print("=" * 50)

    try:
        from modules.smart_search_helper import SmartSearchHelper

        print("✅ 智能搜索助手模块导入成功")

        # 设置下载目录
        import os
        download_dir = os.path.join(os.getcwd(), "downloads", "test")
        os.makedirs(download_dir, exist_ok=True)

        print(f"📁 下载目录: {download_dir}")
        print(f"📚 测试文献数量: {len(test_citations)}")

        # 询问是否测试完整功能
        if input("\n⚠️  这将启动浏览器并自动搜索下载，是否继续？(y/n): ").lower() == 'y':
            print("🚀 正在启动智能搜索助手...")

            # 创建助手实例
            helper = SmartSearchHelper(download_dir)

            # 执行自动搜索和下载
            results = helper.auto_search_and_download(test_citations)

            # 显示结果
            print("\n📊 搜索下载结果:")
            print(f"  总计: {results['total']} 篇")
            print(f"  处理: {results['processed']} 篇")
            print(f"  成功: {results['downloaded']} 篇")
            print(f"  失败: {results['failed']} 篇")
            print(f"  跳过: {results['skipped']} 篇")

            if results['downloaded'] > 0:
                print(f"\n✅ 成功下载 {results['downloaded']} 篇文献的EndNote引用")
                print(f"📁 文件保存在: {download_dir}")
            else:
                print("\n❌ 没有成功下载任何文献")

        print("\n🎉 测试完成！")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_smart_search()
