using System;
using System.Windows.Forms;
using EnhancedStaticRockMechanicsSystem.Services;
using EnhancedStaticRockMechanicsSystem.Forms;

namespace EnhancedStaticRockMechanicsSystem
{
    /// <summary>
    /// 增强版静态岩石力学参数法系统主程序
    /// </summary>
    internal static class Program
    {
        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static void Main()
        {
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);

                // 初始化日志服务
                LoggingService.Instance.Info("增强版静态岩石力学参数法系统启动");

                // 启动测试设计器窗体
                LoggingService.Instance.Info("启动VS2022设计器测试窗体");
                Application.Run(new TestDesignerForm());
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"系统启动失败: {ex.Message}");
                MessageBox.Show($"系统启动失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
