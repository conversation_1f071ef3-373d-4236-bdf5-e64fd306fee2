using System;
using System.Collections.Generic;

namespace EnhancedStaticRockMechanicsAnalysisSystem.Models
{
    /// <summary>
    /// 计算结果
    /// </summary>
    public class CalculationResult
    {
        /// <summary>
        /// 杨氏模量 (GPa)
        /// </summary>
        public double YoungModulus { get; set; }

        /// <summary>
        /// 泊松比
        /// </summary>
        public double PoissonRatio { get; set; }

        /// <summary>
        /// 脆性指数
        /// </summary>
        public double BrittlenessIndex { get; set; }

        /// <summary>
        /// 体积模量 (GPa)
        /// </summary>
        public double BulkModulus { get; set; }

        /// <summary>
        /// 剪切模量 (GPa)
        /// </summary>
        public double ShearModulus { get; set; }

        /// <summary>
        /// 计算是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 计算时间
        /// </summary>
        public DateTime CalculationTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 输入参数
        /// </summary>
        public Dictionary<string, double> InputParameters { get; set; } = new Dictionary<string, double>();

        /// <summary>
        /// 中间计算步骤
        /// </summary>
        public List<string> CalculationSteps { get; set; } = new List<string>();

        public CalculationResult()
        {
            IsSuccess = true;
        }

        public CalculationResult(double youngModulus, double poissonRatio, double brittlenessIndex)
        {
            YoungModulus = youngModulus;
            PoissonRatio = poissonRatio;
            BrittlenessIndex = brittlenessIndex;
            IsSuccess = true;
        }

        /// <summary>
        /// 创建失败的计算结果
        /// </summary>
        /// <param name="errorMessage"></param>
        /// <returns></returns>
        public static CalculationResult CreateFailure(string errorMessage)
        {
            return new CalculationResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage
            };
        }

        /// <summary>
        /// 添加计算步骤
        /// </summary>
        /// <param name="step"></param>
        public void AddCalculationStep(string step)
        {
            CalculationSteps.Add($"{DateTime.Now:HH:mm:ss.fff}: {step}");
        }

        /// <summary>
        /// 添加输入参数
        /// </summary>
        /// <param name="name"></param>
        /// <param name="value"></param>
        public void AddInputParameter(string name, double value)
        {
            InputParameters[name] = value;
        }

        /// <summary>
        /// 获取结果摘要
        /// </summary>
        /// <returns></returns>
        public string GetSummary()
        {
            if (!IsSuccess)
            {
                return $"计算失败: {ErrorMessage}";
            }

            return $"杨氏模量: {YoungModulus:F2} GPa, 泊松比: {PoissonRatio:F3}, 脆性指数: {BrittlenessIndex:F3}";
        }

        /// <summary>
        /// 验证结果有效性
        /// </summary>
        /// <returns></returns>
        public bool IsValidResult()
        {
            return IsSuccess && 
                   YoungModulus > 0 && 
                   PoissonRatio >= 0 && PoissonRatio <= 0.5 && 
                   BrittlenessIndex >= 0 && BrittlenessIndex <= 1;
        }
    }
}
