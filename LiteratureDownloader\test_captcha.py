#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试人机验证处理功能
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_captcha_detection():
    """测试验证码检测功能"""
    print("🔍 测试人机验证检测功能")
    print("=" * 40)
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from modules.captcha_handler import CaptchaHandler
        
        # 创建浏览器
        chrome_options = Options()
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        
        driver = webdriver.Chrome(options=chrome_options)
        
        # 创建验证码处理器
        captcha_handler = CaptchaHandler(driver)
        
        print("✅ 浏览器和验证码处理器创建成功")
        
        # 测试网站列表
        test_sites = [
            "https://webofscience.clarivate.cn",
            "https://www.google.com/recaptcha/api2/demo",  # reCAPTCHA测试页面
        ]
        
        for site in test_sites:
            print(f"\n🌐 测试网站: {site}")
            
            try:
                driver.get(site)
                time.sleep(3)
                
                # 检测验证码
                has_captcha = captcha_handler.detect_captcha()
                
                if has_captcha:
                    print("🚨 检测到人机验证")
                    
                    # 询问是否测试处理
                    if input("是否测试自动处理？(y/n): ").lower() == 'y':
                        print("🤖 开始自动处理验证码...")
                        
                        success = captcha_handler.handle_captcha()
                        
                        if success:
                            print("✅ 验证码处理成功")
                        else:
                            print("❌ 验证码处理失败")
                else:
                    print("✅ 未检测到人机验证")
                    
            except Exception as e:
                print(f"❌ 测试 {site} 失败: {e}")
        
        print("\n🎯 测试完成，关闭浏览器...")
        driver.quit()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_smart_search_with_captcha():
    """测试智能搜索助手的验证码处理"""
    print("\n🤖 测试智能搜索助手验证码处理")
    print("=" * 50)
    
    # 测试数据
    test_citations = [
        {
            'reference_number': '1',
            'title': 'Machine learning applications in data science',
            'authors': ['Smith, J.', 'Johnson, A.'],
            'year': '2023',
            'journal': 'Nature'
        }
    ]
    
    try:
        from modules.smart_search_helper import SmartSearchHelper
        import os
        
        # 设置下载目录
        download_dir = os.path.join(os.getcwd(), "downloads", "test_captcha")
        os.makedirs(download_dir, exist_ok=True)
        
        print(f"📁 下载目录: {download_dir}")
        
        if input("⚠️  这将启动浏览器测试验证码处理，是否继续？(y/n): ").lower() == 'y':
            print("🚀 启动智能搜索助手...")
            
            helper = SmartSearchHelper(download_dir)
            
            # 只测试一篇文献
            results = helper.auto_search_and_download(test_citations[:1])
            
            print("\n📊 测试结果:")
            print(f"  总计: {results['total']} 篇")
            print(f"  处理: {results['processed']} 篇")
            print(f"  成功: {results['downloaded']} 篇")
            print(f"  失败: {results['failed']} 篇")
            print(f"  跳过: {results['skipped']} 篇")
            
            if results['downloaded'] > 0:
                print(f"\n✅ 验证码处理和文献下载成功")
            else:
                print(f"\n⚠️  测试完成，但未成功下载文献")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🧪 人机验证处理测试工具")
    print("=" * 50)
    
    print("选择测试模式:")
    print("1. 验证码检测功能测试")
    print("2. 智能搜索助手验证码处理测试")
    print("3. 全部测试")
    
    choice = input("\n请选择 (1/2/3): ").strip()
    
    if choice == '1':
        test_captcha_detection()
    elif choice == '2':
        test_smart_search_with_captcha()
    elif choice == '3':
        test_captcha_detection()
        test_smart_search_with_captcha()
    else:
        print("❌ 无效选择")
    
    print("\n🎉 测试程序结束")

if __name__ == "__main__":
    main()
