using System;
using System.Data;
using System.IO;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;

namespace EnhancedStaticRockMechanicsSystem.Services
{
    /// <summary>
    /// 数据导入服务
    /// </summary>
    public class ImportService
    {
        /// <summary>
        /// 从Excel文件导入数据
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>导入的数据表</returns>
        public DataTable ImportFromExcel(string filePath)
        {
            LoggingService.Instance.Info($"开始导入Excel文件: {filePath}");
            try
            {
                if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
                {
                    LoggingService.Instance.Error($"文件不存在: {filePath}");
                    throw new ArgumentException("文件不存在");
                }

                // 创建数据表
                DataTable dataTable = new DataTable();
                LoggingService.Instance.Debug("已创建数据表");

                // 根据文件扩展名创建工作簿
                IWorkbook workbook;
                string fileExtension = Path.GetExtension(filePath).ToLower();
                LoggingService.Instance.Debug($"文件扩展名: {fileExtension}");

                using (FileStream fs = new FileStream(filePath, FileMode.Open, FileAccess.Read))
                {
                    if (fileExtension == ".xlsx")
                    {
                        LoggingService.Instance.Debug("使用XSSFWorkbook处理.xlsx文件");
                        workbook = new XSSFWorkbook(fs);
                    }
                    else if (fileExtension == ".xls")
                    {
                        LoggingService.Instance.Debug("使用HSSFWorkbook处理.xls文件");
                        workbook = new HSSFWorkbook(fs);
                    }
                    else
                    {
                        LoggingService.Instance.Error($"不支持的文件格式: {fileExtension}");
                        throw new ArgumentException("不支持的文件格式");
                    }
                }

                // 获取第一个工作表
                LoggingService.Instance.Debug("尝试获取第一个工作表");
                ISheet sheet = workbook.GetSheetAt(0);
                if (sheet == null)
                {
                    LoggingService.Instance.Error("工作表为空");
                    throw new ArgumentException("工作表为空");
                }
                LoggingService.Instance.Debug($"成功获取工作表: {sheet.SheetName}");

                // 获取标题行
                LoggingService.Instance.Debug("尝试获取标题行");
                IRow headerRow = sheet.GetRow(0);
                if (headerRow == null)
                {
                    LoggingService.Instance.Error("标题行为空");
                    throw new ArgumentException("标题行为空");
                }
                LoggingService.Instance.Debug($"成功获取标题行，列数: {headerRow.LastCellNum}");

                // 添加列
                LoggingService.Instance.Debug("开始添加数据列");
                for (int i = 0; i < headerRow.LastCellNum; i++)
                {
                    ICell cell = headerRow.GetCell(i);
                    if (cell != null)
                    {
                        string columnName = cell.ToString();
                        if (!string.IsNullOrEmpty(columnName))
                        {
                            dataTable.Columns.Add(columnName);
                            LoggingService.Instance.Debug($"添加列: {columnName}");
                        }
                        else
                        {
                            string defaultName = $"Column{i}";
                            dataTable.Columns.Add(defaultName);
                            LoggingService.Instance.Debug($"添加默认列名: {defaultName}，原列名为空");
                        }
                    }
                    else
                    {
                        string defaultName = $"Column{i}";
                        dataTable.Columns.Add(defaultName);
                        LoggingService.Instance.Debug($"添加默认列名: {defaultName}，单元格为空");
                    }
                }
                LoggingService.Instance.Info($"成功添加{dataTable.Columns.Count}个列");

                // 添加数据行
                LoggingService.Instance.Debug($"开始添加数据行，总行数: {sheet.LastRowNum}");
                int validRowCount = 0;
                for (int i = 1; i <= sheet.LastRowNum; i++)
                {
                    IRow row = sheet.GetRow(i);
                    if (row != null)
                    {
                        DataRow dataRow = dataTable.NewRow();
                        int validCellCount = 0;
                        for (int j = 0; j < headerRow.LastCellNum; j++)
                        {
                            ICell cell = row.GetCell(j);
                            if (cell != null)
                            {
                                try
                                {
                                    switch (cell.CellType)
                                    {
                                        case CellType.Numeric:
                                            if (DateUtil.IsCellDateFormatted(cell))
                                            {
                                                dataRow[j] = cell.DateCellValue;
                                                LoggingService.Instance.Debug($"单元格[{i},{j}]：日期值 {cell.DateCellValue}");
                                            }
                                            else
                                            {
                                                dataRow[j] = cell.NumericCellValue;
                                                LoggingService.Instance.Debug($"单元格[{i},{j}]：数值 {cell.NumericCellValue}");
                                            }
                                            break;
                                        case CellType.String:
                                            dataRow[j] = cell.StringCellValue;
                                            LoggingService.Instance.Debug($"单元格[{i},{j}]：字符串 {cell.StringCellValue}");
                                            break;
                                        case CellType.Boolean:
                                            dataRow[j] = cell.BooleanCellValue;
                                            LoggingService.Instance.Debug($"单元格[{i},{j}]：布尔值 {cell.BooleanCellValue}");
                                            break;
                                        case CellType.Formula:
                                            LoggingService.Instance.Debug($"单元格[{i},{j}]：公式 {cell.CellFormula}");
                                            switch (cell.CachedFormulaResultType)
                                            {
                                                case CellType.Numeric:
                                                    dataRow[j] = cell.NumericCellValue;
                                                    LoggingService.Instance.Debug($"单元格[{i},{j}]：公式结果(数值) {cell.NumericCellValue}");
                                                    break;
                                                case CellType.String:
                                                    dataRow[j] = cell.StringCellValue;
                                                    LoggingService.Instance.Debug($"单元格[{i},{j}]：公式结果(字符串) {cell.StringCellValue}");
                                                    break;
                                                default:
                                                    dataRow[j] = cell.ToString();
                                                    LoggingService.Instance.Debug($"单元格[{i},{j}]：公式结果(其他) {cell.ToString()}");
                                                    break;
                                            }
                                            break;
                                        default:
                                            dataRow[j] = cell.ToString();
                                            LoggingService.Instance.Debug($"单元格[{i},{j}]：其他类型 {cell.ToString()}");
                                            break;
                                    }
                                    validCellCount++;
                                }
                                catch (Exception cellEx)
                                {
                                    LoggingService.Instance.Warning($"处理单元格[{i},{j}]时出错: {cellEx.Message}");
                                    dataRow[j] = null;
                                }
                            }
                            else
                            {
                                LoggingService.Instance.Debug($"单元格[{i},{j}]为空");
                            }
                        }
                        dataTable.Rows.Add(dataRow);
                        validRowCount++;
                        LoggingService.Instance.Debug($"添加第{i}行数据，有效单元格数: {validCellCount}");
                    }
                    else
                    {
                        LoggingService.Instance.Debug($"第{i}行为空，已跳过");
                    }
                }
                LoggingService.Instance.Info($"成功导入{validRowCount}行数据");

                LoggingService.Instance.Info($"Excel导入完成，共{dataTable.Rows.Count}行，{dataTable.Columns.Count}列");
                return dataTable;
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"导入Excel文件失败: {filePath}, 错误: {ex.Message}");
                throw;
            }
            finally
            {
                LoggingService.Instance.Debug("Excel导入过程结束");
            }
        }
    }
}
