#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文献引用提取与下载系统 - 简单启动脚本
双击此文件或运行 python run.py 即可启动程序
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """主函数"""
    print("🚀 启动文献引用提取与下载系统...")
    
    try:
        # 导入并启动GUI
        from gui.main_window import LiteratureCitationApp
        
        app = LiteratureCitationApp()
        app.run()
        
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        print("\n可能的解决方案:")
        print("1. 安装缺少的依赖包:")
        print("   pip install Pillow pytesseract selenium loguru")
        print("2. 检查Python版本是否为3.7+")
        input("\n按回车键退出...")
        
    except Exception as e:
        print(f"❌ 程序启动失败: {e}")
        input("\n按回车键退出...")

if __name__ == "__main__":
    main()
