#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文献引用提取与下载系统 - 启动程序
直接运行此文件即可启动程序
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_dependencies():
    """检查依赖包"""
    missing_packages = []
    
    try:
        import tkinter
    except ImportError:
        missing_packages.append("tkinter (通常随Python安装)")
    
    try:
        import PIL
    except ImportError:
        missing_packages.append("Pillow")
    
    try:
        import pytesseract
    except ImportError:
        missing_packages.append("pytesseract")
    
    try:
        import selenium
    except ImportError:
        missing_packages.append("selenium")
    
    try:
        import loguru
    except ImportError:
        missing_packages.append("loguru")
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for pkg in missing_packages:
            print(f"   - {pkg}")
        print("\n请运行以下命令安装:")
        print("pip install Pillow pytesseract selenium loguru")
        return False
    
    return True

def check_tesseract():
    """检查Tesseract OCR"""
    try:
        import pytesseract
        # 尝试获取版本信息
        pytesseract.get_tesseract_version()
        return True
    except Exception:
        print("❌ Tesseract OCR未正确安装")
        print("请下载并安装Tesseract OCR:")
        print("https://github.com/UB-Mannheim/tesseract/wiki")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("📚 文献引用提取与下载系统")
    print("=" * 50)
    
    # 检查依赖
    print("🔍 检查系统依赖...")
    
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请安装缺少的包后重试")
        input("按回车键退出...")
        return
    
    if not check_tesseract():
        print("\n⚠️  OCR功能可能无法使用，但程序仍可运行")
        print("如需使用截图识别功能，请安装Tesseract OCR")
    
    print("✅ 依赖检查完成")
    
    # 启动GUI
    print("\n🚀 启动程序界面...")
    try:
        from gui.main_window import LiteratureCitationApp
        
        # 创建并运行应用
        app = LiteratureCitationApp()
        app.run()
        
    except Exception as e:
        print(f"❌ 程序启动失败: {e}")
        print("\n请检查:")
        print("1. 是否安装了所有依赖包")
        print("2. Python版本是否为3.7+")
        print("3. 是否有其他错误信息")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
