using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using EnhancedStaticRockMechanicsAnalysisSystem.Models;

namespace EnhancedStaticRockMechanicsAnalysisSystem.Services
{
    /// <summary>
    /// 数据导入服务
    /// </summary>
    public class ImportService
    {
        /// <summary>
        /// 从CSV文件导入数据
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>导入的数据点列表</returns>
        public List<RockMechanicsDataPoint> ImportFromCsv(string filePath)
        {
            var dataPoints = new List<RockMechanicsDataPoint>();
            
            try
            {
                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException($"文件不存在: {filePath}");
                }

                var lines = File.ReadAllLines(filePath);
                if (lines.Length < 2) // 至少需要标题行和一行数据
                {
                    throw new InvalidDataException("CSV文件格式不正确或没有数据");
                }

                // 跳过标题行
                for (int i = 1; i < lines.Length; i++)
                {
                    var line = lines[i].Trim();
                    if (string.IsNullOrEmpty(line))
                        continue;

                    var dataPoint = ParseCsvLine(line, i + 1);
                    if (dataPoint != null)
                    {
                        dataPoints.Add(dataPoint);
                    }
                }

                LoggingService.Instance.Info($"从CSV文件成功导入 {dataPoints.Count} 条数据: {filePath}");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"CSV文件导入失败: {filePath}", ex);
                throw;
            }

            return dataPoints;
        }

        /// <summary>
        /// 解析CSV行数据
        /// </summary>
        /// <param name="line">CSV行</param>
        /// <param name="lineNumber">行号</param>
        /// <returns>数据点</returns>
        private RockMechanicsDataPoint? ParseCsvLine(string line, int lineNumber)
        {
            try
            {
                var values = line.Split(',');
                if (values.Length < 3)
                {
                    LoggingService.Instance.Warning($"CSV第{lineNumber}行数据不完整，跳过");
                    return null;
                }

                // 解析基本参数：密度、纵波速度、横波速度
                if (!double.TryParse(values[0].Trim(), out double density) ||
                    !double.TryParse(values[1].Trim(), out double vp) ||
                    !double.TryParse(values[2].Trim(), out double vs))
                {
                    LoggingService.Instance.Warning($"CSV第{lineNumber}行数据格式错误，跳过");
                    return null;
                }

                var dataPoint = new RockMechanicsDataPoint(density, vp, vs);

                // 如果有更多列，尝试解析计算结果
                if (values.Length > 3 && double.TryParse(values[3].Trim(), out double youngModulus))
                {
                    dataPoint.YoungModulus = youngModulus;
                }

                if (values.Length > 4 && double.TryParse(values[4].Trim(), out double poissonRatio))
                {
                    dataPoint.PoissonRatio = poissonRatio;
                }

                if (values.Length > 5 && double.TryParse(values[5].Trim(), out double brittlenessIndex))
                {
                    dataPoint.BrittlenessIndex = brittlenessIndex;
                }

                // 验证数据有效性
                if (!dataPoint.IsValid())
                {
                    LoggingService.Instance.Warning($"CSV第{lineNumber}行数据无效，跳过");
                    return null;
                }

                return dataPoint;
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Warning($"解析CSV第{lineNumber}行时发生错误: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 从Excel文件导入数据
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>导入的数据点列表</returns>
        public List<RockMechanicsDataPoint> ImportFromExcel(string filePath)
        {
            var dataPoints = new List<RockMechanicsDataPoint>();
            
            try
            {
                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException($"文件不存在: {filePath}");
                }

                // 这里可以使用NPOI或EPPlus库来读取Excel文件
                // 为了简化，这里先返回空列表，实际项目中需要实现Excel读取逻辑
                LoggingService.Instance.Info("Excel导入功能正在开发中");
                throw new NotImplementedException("Excel导入功能正在开发中");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"Excel文件导入失败: {filePath}", ex);
                throw;
            }
        }

        /// <summary>
        /// 验证导入文件格式
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>验证结果</returns>
        public (bool IsValid, string Message) ValidateImportFile(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    return (false, "文件不存在");
                }

                var extension = Path.GetExtension(filePath).ToLower();
                if (extension != ".csv" && extension != ".xlsx" && extension != ".xls")
                {
                    return (false, "不支持的文件格式，请选择CSV或Excel文件");
                }

                var fileInfo = new FileInfo(filePath);
                if (fileInfo.Length == 0)
                {
                    return (false, "文件为空");
                }

                if (fileInfo.Length > 10 * 1024 * 1024) // 10MB限制
                {
                    return (false, "文件过大，请选择小于10MB的文件");
                }

                // 对于CSV文件，检查基本格式
                if (extension == ".csv")
                {
                    var lines = File.ReadLines(filePath).Take(5).ToArray();
                    if (lines.Length < 2)
                    {
                        return (false, "CSV文件至少需要包含标题行和一行数据");
                    }

                    // 检查第一行数据格式
                    if (lines.Length > 1)
                    {
                        var values = lines[1].Split(',');
                        if (values.Length < 3)
                        {
                            return (false, "CSV文件格式不正确，至少需要3列数据（密度、纵波速度、横波速度）");
                        }
                    }
                }

                return (true, "文件格式验证通过");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"验证导入文件时发生错误: {filePath}", ex);
                return (false, $"文件验证失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取支持的文件格式
        /// </summary>
        /// <returns>文件格式过滤器</returns>
        public string GetSupportedFileFilter()
        {
            return "支持的文件|*.csv;*.xlsx;*.xls|CSV文件|*.csv|Excel文件|*.xlsx;*.xls|所有文件|*.*";
        }

        /// <summary>
        /// 批量导入多个文件
        /// </summary>
        /// <param name="filePaths">文件路径列表</param>
        /// <returns>导入结果</returns>
        public (List<RockMechanicsDataPoint> DataPoints, List<string> Errors) ImportMultipleFiles(string[] filePaths)
        {
            var allDataPoints = new List<RockMechanicsDataPoint>();
            var errors = new List<string>();

            foreach (var filePath in filePaths)
            {
                try
                {
                    var extension = Path.GetExtension(filePath).ToLower();
                    List<RockMechanicsDataPoint> dataPoints;

                    if (extension == ".csv")
                    {
                        dataPoints = ImportFromCsv(filePath);
                    }
                    else if (extension == ".xlsx" || extension == ".xls")
                    {
                        dataPoints = ImportFromExcel(filePath);
                    }
                    else
                    {
                        errors.Add($"不支持的文件格式: {filePath}");
                        continue;
                    }

                    allDataPoints.AddRange(dataPoints);
                    LoggingService.Instance.Info($"成功导入文件: {filePath}, 数据点数量: {dataPoints.Count}");
                }
                catch (Exception ex)
                {
                    var errorMessage = $"导入文件失败: {filePath} - {ex.Message}";
                    errors.Add(errorMessage);
                    LoggingService.Instance.Error(errorMessage, ex);
                }
            }

            return (allDataPoints, errors);
        }
    }
}
