using System;
using System.Drawing;
using System.Windows.Forms;

namespace EnhancedStaticRockMechanicsAnalysisSystem.Core
{
    /// <summary>
    /// 窗体适配器 - 提供通用的窗体操作功能
    /// </summary>
    public static class FormAdapter
    {
        /// <summary>
        /// 应用深色主题到窗体
        /// </summary>
        /// <param name="form"></param>
        public static void ApplyDarkTheme(Form form)
        {
            form.BackColor = Color.FromArgb(33, 33, 33);
            form.ForeColor = Color.White;
            
            ApplyDarkThemeToControls(form.Controls);
        }

        /// <summary>
        /// 递归应用深色主题到控件
        /// </summary>
        /// <param name="controls"></param>
        private static void ApplyDarkThemeToControls(Control.ControlCollection controls)
        {
            foreach (Control control in controls)
            {
                switch (control)
                {
                    case Panel panel:
                        panel.BackColor = Color.FromArgb(33, 33, 33);
                        panel.ForeColor = Color.White;
                        break;
                        
                    case Label label:
                        if (label.BackColor != Color.FromArgb(45, 45, 45)) // 保持标题栏颜色
                            label.BackColor = Color.Transparent;
                        label.ForeColor = Color.White;
                        break;
                        
                    case TextBox textBox:
                        textBox.BackColor = Color.FromArgb(45, 45, 45);
                        textBox.ForeColor = Color.White;
                        textBox.BorderStyle = BorderStyle.FixedSingle;
                        break;
                        
                    case Button button:
                        if (button.BackColor == SystemColors.Control) // 默认按钮颜色
                        {
                            button.BackColor = Color.FromArgb(60, 60, 60);
                        }
                        button.ForeColor = Color.White;
                        button.FlatStyle = FlatStyle.Flat;
                        break;
                        
                    case DataGridView dgv:
                        dgv.BackgroundColor = Color.FromArgb(45, 45, 45);
                        dgv.DefaultCellStyle.BackColor = Color.FromArgb(45, 45, 45);
                        dgv.DefaultCellStyle.ForeColor = Color.White;
                        dgv.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(60, 60, 60);
                        dgv.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
                        dgv.EnableHeadersVisualStyles = false;
                        break;
                }
                
                // 递归处理子控件
                if (control.HasChildren)
                {
                    ApplyDarkThemeToControls(control.Controls);
                }
            }
        }

        /// <summary>
        /// 设置窗体居中显示
        /// </summary>
        /// <param name="form"></param>
        public static void CenterForm(Form form)
        {
            form.StartPosition = FormStartPosition.CenterScreen;
        }

        /// <summary>
        /// 设置窗体最大化显示
        /// </summary>
        /// <param name="form"></param>
        public static void MaximizeForm(Form form)
        {
            form.WindowState = FormWindowState.Maximized;
        }

        /// <summary>
        /// 显示错误消息
        /// </summary>
        /// <param name="message"></param>
        /// <param name="title"></param>
        public static void ShowError(string message, string title = "错误")
        {
            MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        /// <summary>
        /// 显示信息消息
        /// </summary>
        /// <param name="message"></param>
        /// <param name="title"></param>
        public static void ShowInfo(string message, string title = "信息")
        {
            MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 显示警告消息
        /// </summary>
        /// <param name="message"></param>
        /// <param name="title"></param>
        public static void ShowWarning(string message, string title = "警告")
        {
            MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }

        /// <summary>
        /// 显示确认对话框
        /// </summary>
        /// <param name="message"></param>
        /// <param name="title"></param>
        /// <returns></returns>
        public static bool ShowConfirm(string message, string title = "确认")
        {
            return MessageBox.Show(message, title, MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes;
        }

        /// <summary>
        /// 设置控件的工具提示
        /// </summary>
        /// <param name="control"></param>
        /// <param name="tooltip"></param>
        /// <param name="text"></param>
        public static void SetToolTip(Control control, ToolTip tooltip, string text)
        {
            tooltip.SetToolTip(control, text);
        }

        /// <summary>
        /// 验证数值输入
        /// </summary>
        /// <param name="textBox"></param>
        /// <param name="value"></param>
        /// <param name="fieldName"></param>
        /// <returns></returns>
        public static bool ValidateNumericInput(TextBox textBox, out double value, string fieldName)
        {
            if (double.TryParse(textBox.Text.Trim(), out value))
            {
                textBox.BackColor = Color.FromArgb(45, 45, 45);
                return true;
            }
            else
            {
                textBox.BackColor = Color.FromArgb(139, 0, 0); // 深红色表示错误
                ShowError($"请输入有效的{fieldName}数值");
                textBox.Focus();
                return false;
            }
        }

        /// <summary>
        /// 清空文本框并重置颜色
        /// </summary>
        /// <param name="textBoxes"></param>
        public static void ClearTextBoxes(params TextBox[] textBoxes)
        {
            foreach (var textBox in textBoxes)
            {
                textBox.Clear();
                textBox.BackColor = Color.FromArgb(45, 45, 45);
            }
        }

        /// <summary>
        /// 设置按钮为主要按钮样式
        /// </summary>
        /// <param name="button"></param>
        public static void SetPrimaryButtonStyle(Button button)
        {
            button.BackColor = Color.FromArgb(0, 120, 215);
            button.ForeColor = Color.White;
            button.FlatStyle = FlatStyle.Flat;
        }

        /// <summary>
        /// 设置按钮为次要按钮样式
        /// </summary>
        /// <param name="button"></param>
        public static void SetSecondaryButtonStyle(Button button)
        {
            button.BackColor = Color.FromArgb(60, 60, 60);
            button.ForeColor = Color.White;
            button.FlatStyle = FlatStyle.Flat;
        }

        /// <summary>
        /// 设置按钮为危险按钮样式
        /// </summary>
        /// <param name="button"></param>
        public static void SetDangerButtonStyle(Button button)
        {
            button.BackColor = Color.FromArgb(220, 53, 69);
            button.ForeColor = Color.White;
            button.FlatStyle = FlatStyle.Flat;
        }
    }
}
