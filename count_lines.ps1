$totalLines = 0
$fileCount = 0
Write-Host "统计源代码文件行数..."
Write-Host "========================"

# 统计.cs文件
Get-ChildItem -Path "MineralCompositionSystem_Final" -Recurse -Include "*.cs" | ForEach-Object {
    $lines = (Get-Content $_.FullName).Count
    $totalLines += $lines
    $fileCount++
    Write-Host "$($_.Name): $lines 行"
}

Write-Host "========================"
Write-Host "总计: $fileCount 个.cs文件, $totalLines 行代码"

# 统计其他相关文件
Write-Host ""
Write-Host "其他项目文件:"
Write-Host "========================"
$otherLines = 0
$otherCount = 0

# 项目文件
Get-ChildItem -Path "MineralCompositionSystem_Final" -Recurse -Include "*.csproj","*.html","*.css","*.md","*.bat" | ForEach-Object {
    $lines = (Get-Content $_.FullName).Count
    $otherLines += $lines
    $otherCount++
    Write-Host "$($_.Name): $lines 行"
}

Write-Host "========================"
Write-Host "其他文件: $otherCount 个文件, $otherLines 行"
Write-Host "项目总计: $($fileCount + $otherCount) 个文件, $($totalLines + $otherLines) 行"
