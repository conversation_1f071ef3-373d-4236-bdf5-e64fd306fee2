using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Text;
using EnhancedStaticRockMechanicsAnalysisSystem.Models;

namespace EnhancedStaticRockMechanicsAnalysisSystem.Services
{
    /// <summary>
    /// 数据导出服务
    /// </summary>
    public class ExportService
    {
        /// <summary>
        /// 导出数据到CSV文件
        /// </summary>
        /// <param name="dataPoints">数据点列表</param>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否成功</returns>
        public bool ExportToCsv(List<RockMechanicsDataPoint> dataPoints, string filePath)
        {
            try
            {
                var csv = new StringBuilder();
                
                // 添加标题行
                csv.AppendLine("序号,密度(g/cm³),纵波速度(m/s),横波速度(m/s),杨氏模量(GPa),泊松比,脆性指数,创建时间,备注");
                
                // 添加数据行
                for (int i = 0; i < dataPoints.Count; i++)
                {
                    var dataPoint = dataPoints[i];
                    csv.AppendLine($"{i + 1}," +
                                  $"{dataPoint.Density:F3}," +
                                  $"{dataPoint.VpVelocity:F0}," +
                                  $"{dataPoint.VsVelocity:F0}," +
                                  $"{dataPoint.YoungModulus:F2}," +
                                  $"{dataPoint.PoissonRatio:F4}," +
                                  $"{dataPoint.BrittlenessIndex:F4}," +
                                  $"{dataPoint.CreatedTime:yyyy-MM-dd HH:mm:ss}," +
                                  $"\"{dataPoint.Remarks ?? ""}\"");
                }
                
                // 写入文件
                File.WriteAllText(filePath, csv.ToString(), Encoding.UTF8);
                
                LoggingService.Instance.Info($"成功导出 {dataPoints.Count} 条数据到CSV文件: {filePath}");
                return true;
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"导出CSV文件失败: {filePath}", ex);
                return false;
            }
        }

        /// <summary>
        /// 导出数据到Excel文件
        /// </summary>
        /// <param name="dataPoints">数据点列表</param>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否成功</returns>
        public bool ExportToExcel(List<RockMechanicsDataPoint> dataPoints, string filePath)
        {
            try
            {
                // 这里可以使用NPOI或EPPlus库来创建Excel文件
                // 为了简化，这里先返回false，实际项目中需要实现Excel导出逻辑
                LoggingService.Instance.Info("Excel导出功能正在开发中");
                throw new NotImplementedException("Excel导出功能正在开发中");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"导出Excel文件失败: {filePath}", ex);
                return false;
            }
        }

        /// <summary>
        /// 导出统计报告
        /// </summary>
        /// <param name="dataPoints">数据点列表</param>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否成功</returns>
        public bool ExportStatisticsReport(List<RockMechanicsDataPoint> dataPoints, string filePath)
        {
            try
            {
                if (dataPoints.Count == 0)
                {
                    throw new InvalidOperationException("没有数据可以导出");
                }

                var report = new StringBuilder();
                
                // 报告标题
                report.AppendLine("增强版静态岩石力学参数分析系统 - 统计报告");
                report.AppendLine("=" + new string('=', 50));
                report.AppendLine($"生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                report.AppendLine($"数据点总数: {dataPoints.Count}");
                report.AppendLine();

                // 基本统计信息
                report.AppendLine("基本统计信息:");
                report.AppendLine("-" + new string('-', 30));
                
                // 密度统计
                var densities = dataPoints.ConvertAll(dp => dp.Density);
                report.AppendLine($"密度 (g/cm³):");
                report.AppendLine($"  最小值: {densities.Min():F3}");
                report.AppendLine($"  最大值: {densities.Max():F3}");
                report.AppendLine($"  平均值: {densities.Average():F3}");
                report.AppendLine();

                // 纵波速度统计
                var vpVelocities = dataPoints.ConvertAll(dp => dp.VpVelocity);
                report.AppendLine($"纵波速度 (m/s):");
                report.AppendLine($"  最小值: {vpVelocities.Min():F0}");
                report.AppendLine($"  最大值: {vpVelocities.Max():F0}");
                report.AppendLine($"  平均值: {vpVelocities.Average():F0}");
                report.AppendLine();

                // 横波速度统计
                var vsVelocities = dataPoints.ConvertAll(dp => dp.VsVelocity);
                report.AppendLine($"横波速度 (m/s):");
                report.AppendLine($"  最小值: {vsVelocities.Min():F0}");
                report.AppendLine($"  最大值: {vsVelocities.Max():F0}");
                report.AppendLine($"  平均值: {vsVelocities.Average():F0}");
                report.AppendLine();

                // 脆性指数统计
                var brittlenessIndices = dataPoints.ConvertAll(dp => dp.BrittlenessIndex);
                report.AppendLine($"脆性指数:");
                report.AppendLine($"  最小值: {brittlenessIndices.Min():F4}");
                report.AppendLine($"  最大值: {brittlenessIndices.Max():F4}");
                report.AppendLine($"  平均值: {brittlenessIndices.Average():F4}");
                
                // 计算标准差
                double avgBrittleness = brittlenessIndices.Average();
                double variance = brittlenessIndices.ConvertAll(bi => Math.Pow(bi - avgBrittleness, 2)).Average();
                double stdDev = Math.Sqrt(variance);
                report.AppendLine($"  标准差: {stdDev:F4}");
                report.AppendLine();

                // 脆性指数分布
                report.AppendLine("脆性指数分布:");
                report.AppendLine("-" + new string('-', 30));
                int extremeBrittle = brittlenessIndices.FindAll(bi => bi >= 0.8).Count;
                int highBrittle = brittlenessIndices.FindAll(bi => bi >= 0.6 && bi < 0.8).Count;
                int mediumBrittle = brittlenessIndices.FindAll(bi => bi >= 0.4 && bi < 0.6).Count;
                int lowBrittle = brittlenessIndices.FindAll(bi => bi >= 0.2 && bi < 0.4).Count;
                int ductile = brittlenessIndices.FindAll(bi => bi < 0.2).Count;

                report.AppendLine($"极脆性岩石 (≥0.8): {extremeBrittle} 个 ({(double)extremeBrittle / dataPoints.Count * 100:F1}%)");
                report.AppendLine($"高脆性岩石 (0.6-0.8): {highBrittle} 个 ({(double)highBrittle / dataPoints.Count * 100:F1}%)");
                report.AppendLine($"中等脆性岩石 (0.4-0.6): {mediumBrittle} 个 ({(double)mediumBrittle / dataPoints.Count * 100:F1}%)");
                report.AppendLine($"低脆性岩石 (0.2-0.4): {lowBrittle} 个 ({(double)lowBrittle / dataPoints.Count * 100:F1}%)");
                report.AppendLine($"韧性岩石 (<0.2): {ductile} 个 ({(double)ductile / dataPoints.Count * 100:F1}%)");
                report.AppendLine();

                // 详细数据
                report.AppendLine("详细数据:");
                report.AppendLine("-" + new string('-', 30));
                report.AppendLine("序号\t密度\t纵波速度\t横波速度\t杨氏模量\t泊松比\t脆性指数\t创建时间");
                
                for (int i = 0; i < dataPoints.Count; i++)
                {
                    var dp = dataPoints[i];
                    report.AppendLine($"{i + 1}\t{dp.Density:F3}\t{dp.VpVelocity:F0}\t{dp.VsVelocity:F0}\t" +
                                    $"{dp.YoungModulus:F2}\t{dp.PoissonRatio:F4}\t{dp.BrittlenessIndex:F4}\t" +
                                    $"{dp.CreatedTime:yyyy-MM-dd HH:mm:ss}");
                }

                // 写入文件
                File.WriteAllText(filePath, report.ToString(), Encoding.UTF8);
                
                LoggingService.Instance.Info($"成功导出统计报告: {filePath}");
                return true;
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"导出统计报告失败: {filePath}", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取支持的导出格式
        /// </summary>
        /// <returns>文件格式过滤器</returns>
        public string GetExportFileFilter()
        {
            return "CSV文件|*.csv|文本报告|*.txt|所有文件|*.*";
        }

        /// <summary>
        /// 生成默认文件名
        /// </summary>
        /// <param name="prefix">文件名前缀</param>
        /// <param name="extension">文件扩展名</param>
        /// <returns>默认文件名</returns>
        public string GenerateDefaultFileName(string prefix, string extension)
        {
            return $"{prefix}_{DateTime.Now:yyyyMMdd_HHmmss}.{extension.TrimStart('.')}";
        }

        /// <summary>
        /// 验证导出路径
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>验证结果</returns>
        public (bool IsValid, string Message) ValidateExportPath(string filePath)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(filePath))
                {
                    return (false, "请指定导出文件路径");
                }

                var directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    try
                    {
                        Directory.CreateDirectory(directory);
                    }
                    catch
                    {
                        return (false, "无法创建目标目录");
                    }
                }

                // 检查文件是否被占用
                if (File.Exists(filePath))
                {
                    try
                    {
                        using (var stream = File.OpenWrite(filePath))
                        {
                            // 文件可写
                        }
                    }
                    catch
                    {
                        return (false, "目标文件被其他程序占用");
                    }
                }

                return (true, "导出路径验证通过");
            }
            catch (Exception ex)
            {
                return (false, $"路径验证失败: {ex.Message}");
            }
        }
    }
}
