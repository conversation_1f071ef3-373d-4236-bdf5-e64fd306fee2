using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using EnhancedStaticRockMechanicsAnalysisSystem.Models;

namespace EnhancedStaticRockMechanicsAnalysisSystem.Core
{
    /// <summary>
    /// 数据管理器
    /// </summary>
    public class DataManager
    {
        private List<RockMechanicsDataPoint> dataPoints;
        private DataTable dataTable;

        public DataManager()
        {
            dataPoints = new List<RockMechanicsDataPoint>();
            InitializeDataTable();
        }

        /// <summary>
        /// 初始化数据表
        /// </summary>
        private void InitializeDataTable()
        {
            dataTable = new DataTable();
            dataTable.Columns.Add("序号", typeof(int));
            dataTable.Columns.Add("密度(g/cm³)", typeof(double));
            dataTable.Columns.Add("纵波速度(m/s)", typeof(double));
            dataTable.Columns.Add("横波速度(m/s)", typeof(double));
            dataTable.Columns.Add("杨氏模量(GPa)", typeof(double));
            dataTable.Columns.Add("泊松比", typeof(double));
            dataTable.Columns.Add("脆性指数", typeof(double));
            dataTable.Columns.Add("创建时间", typeof(DateTime));
        }

        /// <summary>
        /// 添加数据点
        /// </summary>
        /// <param name="dataPoint"></param>
        public void AddDataPoint(RockMechanicsDataPoint dataPoint)
        {
            dataPoint.Id = dataPoints.Count + 1;
            dataPoints.Add(dataPoint);
            
            // 添加到数据表
            dataTable.Rows.Add(
                dataPoint.Id,
                dataPoint.Density,
                dataPoint.VpVelocity,
                dataPoint.VsVelocity,
                dataPoint.YoungModulus,
                dataPoint.PoissonRatio,
                dataPoint.BrittlenessIndex,
                dataPoint.CreatedTime
            );
        }

        /// <summary>
        /// 删除数据点
        /// </summary>
        /// <param name="id"></param>
        public bool RemoveDataPoint(int id)
        {
            var dataPoint = dataPoints.FirstOrDefault(dp => dp.Id == id);
            if (dataPoint != null)
            {
                dataPoints.Remove(dataPoint);
                
                // 从数据表中删除
                var row = dataTable.Rows.Cast<DataRow>().FirstOrDefault(r => (int)r["序号"] == id);
                if (row != null)
                {
                    dataTable.Rows.Remove(row);
                }
                
                return true;
            }
            return false;
        }

        /// <summary>
        /// 清空所有数据
        /// </summary>
        public void ClearAllData()
        {
            dataPoints.Clear();
            dataTable.Clear();
        }

        /// <summary>
        /// 获取所有数据点
        /// </summary>
        /// <returns></returns>
        public List<RockMechanicsDataPoint> GetAllDataPoints()
        {
            return new List<RockMechanicsDataPoint>(dataPoints);
        }

        /// <summary>
        /// 获取数据表
        /// </summary>
        /// <returns></returns>
        public DataTable GetDataTable()
        {
            return dataTable;
        }

        /// <summary>
        /// 根据ID获取数据点
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public RockMechanicsDataPoint? GetDataPointById(int id)
        {
            return dataPoints.FirstOrDefault(dp => dp.Id == id);
        }

        /// <summary>
        /// 获取数据点数量
        /// </summary>
        /// <returns></returns>
        public int GetDataPointCount()
        {
            return dataPoints.Count;
        }

        /// <summary>
        /// 获取脆性指数统计信息
        /// </summary>
        /// <returns></returns>
        public (double Min, double Max, double Average, double StdDev) GetBrittlenessStatistics()
        {
            if (dataPoints.Count == 0)
                return (0, 0, 0, 0);

            var brittlenessValues = dataPoints.Select(dp => dp.BrittlenessIndex).ToList();
            
            double min = brittlenessValues.Min();
            double max = brittlenessValues.Max();
            double average = brittlenessValues.Average();
            
            double variance = brittlenessValues.Select(val => Math.Pow(val - average, 2)).Average();
            double stdDev = Math.Sqrt(variance);

            return (min, max, average, stdDev);
        }

        /// <summary>
        /// 按脆性指数排序数据点
        /// </summary>
        /// <param name="ascending">是否升序</param>
        /// <returns></returns>
        public List<RockMechanicsDataPoint> GetDataPointsSortedByBrittleness(bool ascending = true)
        {
            if (ascending)
                return dataPoints.OrderBy(dp => dp.BrittlenessIndex).ToList();
            else
                return dataPoints.OrderByDescending(dp => dp.BrittlenessIndex).ToList();
        }

        /// <summary>
        /// 筛选脆性指数在指定范围内的数据点
        /// </summary>
        /// <param name="minBrittleness"></param>
        /// <param name="maxBrittleness"></param>
        /// <returns></returns>
        public List<RockMechanicsDataPoint> FilterByBrittlenessRange(double minBrittleness, double maxBrittleness)
        {
            return dataPoints.Where(dp => dp.BrittlenessIndex >= minBrittleness && 
                                         dp.BrittlenessIndex <= maxBrittleness).ToList();
        }

        /// <summary>
        /// 导出数据为CSV格式字符串
        /// </summary>
        /// <returns></returns>
        public string ExportToCsv()
        {
            var csv = new System.Text.StringBuilder();
            
            // 添加标题行
            csv.AppendLine("序号,密度(g/cm³),纵波速度(m/s),横波速度(m/s),杨氏模量(GPa),泊松比,脆性指数,创建时间");
            
            // 添加数据行
            foreach (var dataPoint in dataPoints)
            {
                csv.AppendLine($"{dataPoint.Id},{dataPoint.Density},{dataPoint.VpVelocity},{dataPoint.VsVelocity}," +
                              $"{dataPoint.YoungModulus:F2},{dataPoint.PoissonRatio:F3},{dataPoint.BrittlenessIndex:F3}," +
                              $"{dataPoint.CreatedTime:yyyy-MM-dd HH:mm:ss}");
            }
            
            return csv.ToString();
        }

        /// <summary>
        /// 验证数据完整性
        /// </summary>
        /// <returns></returns>
        public (bool IsValid, List<string> Issues) ValidateDataIntegrity()
        {
            var issues = new List<string>();
            
            if (dataPoints.Count != dataTable.Rows.Count)
            {
                issues.Add("数据点列表与数据表行数不匹配");
            }
            
            foreach (var dataPoint in dataPoints)
            {
                if (!dataPoint.IsValid())
                {
                    issues.Add($"数据点 {dataPoint.Id} 包含无效数据");
                }
            }
            
            return (issues.Count == 0, issues);
        }
    }
}
