namespace EnhancedStaticRockMechanicsAnalysisSystem.Forms
{
    partial class StaticRockMechanicsForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            lblTitle = new Label();
            pnlInput = new Panel();
            btnBack = new Button();
            lblCalculationResult = new Label();
            btnCalculate = new Button();
            txtVs = new TextBox();
            txtVp = new TextBox();
            txtDensity = new TextBox();
            lblVs = new Label();
            lblVp = new Label();
            lblDensity = new Label();
            pnlData = new Panel();
            dgvMechanicsData = new DataGridView();
            pnlDataButtons = new Panel();
            btnExportData = new Button();
            btnImportData = new Button();
            btnClearData = new Button();
            pnlChart = new Panel();
            pnlInput.SuspendLayout();
            pnlData.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dgvMechanicsData).BeginInit();
            pnlDataButtons.SuspendLayout();
            SuspendLayout();
            // 
            // lblTitle
            // 
            lblTitle.BackColor = Color.FromArgb(45, 45, 45);
            lblTitle.Dock = DockStyle.Top;
            lblTitle.Font = new Font("微软雅黑", 16F, FontStyle.Bold);
            lblTitle.ForeColor = Color.White;
            lblTitle.Location = new Point(0, 0);
            lblTitle.Name = "lblTitle";
            lblTitle.Size = new Size(1415, 60);
            lblTitle.TabIndex = 0;
            lblTitle.Text = "静态岩石力学参数分析";
            lblTitle.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // pnlInput
            // 
            pnlInput.BackColor = Color.FromArgb(33, 33, 33);
            pnlInput.Controls.Add(btnBack);
            pnlInput.Controls.Add(lblCalculationResult);
            pnlInput.Controls.Add(btnCalculate);
            pnlInput.Controls.Add(txtVs);
            pnlInput.Controls.Add(txtVp);
            pnlInput.Controls.Add(txtDensity);
            pnlInput.Controls.Add(lblVs);
            pnlInput.Controls.Add(lblVp);
            pnlInput.Controls.Add(lblDensity);
            pnlInput.Dock = DockStyle.Top;
            pnlInput.Location = new Point(0, 60);
            pnlInput.Name = "pnlInput";
            pnlInput.Size = new Size(1415, 120);
            pnlInput.TabIndex = 1;
            // 
            // btnBack
            // 
            btnBack.BackColor = Color.FromArgb(60, 60, 60);
            btnBack.FlatStyle = FlatStyle.Flat;
            btnBack.Font = new Font("微软雅黑", 10F);
            btnBack.ForeColor = Color.White;
            btnBack.Location = new Point(1264, 28);
            btnBack.Name = "btnBack";
            btnBack.Size = new Size(80, 37);
            btnBack.TabIndex = 8;
            btnBack.Text = "返回";
            btnBack.UseVisualStyleBackColor = false;
            btnBack.Click += btnBack_Click;
            // 
            // lblCalculationResult
            // 
            lblCalculationResult.Font = new Font("微软雅黑", 10F);
            lblCalculationResult.ForeColor = Color.LightGreen;
            lblCalculationResult.Location = new Point(19, 75);
            lblCalculationResult.Name = "lblCalculationResult";
            lblCalculationResult.Size = new Size(800, 30);
            lblCalculationResult.TabIndex = 7;
            lblCalculationResult.Text = "请输入参数进行计算";
            // 
            // btnCalculate
            // 
            btnCalculate.BackColor = Color.FromArgb(0, 120, 215);
            btnCalculate.FlatStyle = FlatStyle.Flat;
            btnCalculate.Font = new Font("微软雅黑", 10F);
            btnCalculate.ForeColor = Color.White;
            btnCalculate.Location = new Point(1086, 26);
            btnCalculate.Name = "btnCalculate";
            btnCalculate.Size = new Size(97, 40);
            btnCalculate.TabIndex = 6;
            btnCalculate.Text = "计算";
            btnCalculate.UseVisualStyleBackColor = false;
            btnCalculate.Click += btnCalculate_Click;
            // 
            // txtVs
            // 
            txtVs.BackColor = Color.FromArgb(45, 45, 45);
            txtVs.BorderStyle = BorderStyle.FixedSingle;
            txtVs.Font = new Font("微软雅黑", 10F);
            txtVs.ForeColor = Color.White;
            txtVs.Location = new Point(733, 28);
            txtVs.Name = "txtVs";
            txtVs.Size = new Size(80, 34);
            txtVs.TabIndex = 5;
            // 
            // txtVp
            // 
            txtVp.BackColor = Color.FromArgb(45, 45, 45);
            txtVp.BorderStyle = BorderStyle.FixedSingle;
            txtVp.Font = new Font("微软雅黑", 10F);
            txtVp.ForeColor = Color.White;
            txtVp.Location = new Point(434, 28);
            txtVp.Name = "txtVp";
            txtVp.Size = new Size(104, 34);
            txtVp.TabIndex = 4;
            // 
            // txtDensity
            // 
            txtDensity.BackColor = Color.FromArgb(45, 45, 45);
            txtDensity.BorderStyle = BorderStyle.FixedSingle;
            txtDensity.Font = new Font("微软雅黑", 10F);
            txtDensity.ForeColor = Color.White;
            txtDensity.Location = new Point(150, 28);
            txtDensity.Name = "txtDensity";
            txtDensity.Size = new Size(98, 34);
            txtDensity.TabIndex = 3;
            // 
            // lblVs
            // 
            lblVs.AutoSize = true;
            lblVs.Font = new Font("微软雅黑", 10F);
            lblVs.ForeColor = Color.White;
            lblVs.Location = new Point(554, 30);
            lblVs.Name = "lblVs";
            lblVs.Size = new Size(163, 27);
            lblVs.TabIndex = 2;
            lblVs.Text = "横波速度(m/s)：";
            // 
            // lblVp
            // 
            lblVp.AutoSize = true;
            lblVp.Font = new Font("微软雅黑", 10F);
            lblVp.ForeColor = Color.White;
            lblVp.Location = new Point(261, 30);
            lblVp.Name = "lblVp";
            lblVp.Size = new Size(163, 27);
            lblVp.TabIndex = 1;
            lblVp.Text = "纵波速度(m/s)：";
            // 
            // lblDensity
            // 
            lblDensity.AutoSize = true;
            lblDensity.Font = new Font("微软雅黑", 10F);
            lblDensity.ForeColor = Color.White;
            lblDensity.Location = new Point(13, 30);
            lblDensity.Name = "lblDensity";
            lblDensity.Size = new Size(145, 27);
            lblDensity.TabIndex = 0;
            lblDensity.Text = "密度(g/cm³)：";
            // 
            // pnlData
            // 
            pnlData.BackColor = Color.FromArgb(33, 33, 33);
            pnlData.Controls.Add(dgvMechanicsData);
            pnlData.Controls.Add(pnlDataButtons);
            pnlData.Dock = DockStyle.Left;
            pnlData.Location = new Point(0, 180);
            pnlData.Name = "pnlData";
            pnlData.Size = new Size(600, 589);
            pnlData.TabIndex = 2;
            // 
            // dgvMechanicsData
            // 
            dgvMechanicsData.BackgroundColor = Color.FromArgb(45, 45, 45);
            dgvMechanicsData.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dgvMechanicsData.Dock = DockStyle.Fill;
            dgvMechanicsData.Location = new Point(0, 0);
            dgvMechanicsData.Name = "dgvMechanicsData";
            dgvMechanicsData.RowHeadersWidth = 62;
            dgvMechanicsData.RowTemplate.Height = 25;
            dgvMechanicsData.Size = new Size(600, 530);
            dgvMechanicsData.TabIndex = 1;
            // 
            // pnlDataButtons
            // 
            pnlDataButtons.Controls.Add(btnExportData);
            pnlDataButtons.Controls.Add(btnImportData);
            pnlDataButtons.Controls.Add(btnClearData);
            pnlDataButtons.Dock = DockStyle.Bottom;
            pnlDataButtons.Location = new Point(0, 530);
            pnlDataButtons.Name = "pnlDataButtons";
            pnlDataButtons.Size = new Size(600, 59);
            pnlDataButtons.TabIndex = 0;
            // 
            // btnExportData
            // 
            btnExportData.BackColor = Color.FromArgb(0, 120, 215);
            btnExportData.FlatStyle = FlatStyle.Flat;
            btnExportData.Font = new Font("微软雅黑", 9F);
            btnExportData.ForeColor = Color.White;
            btnExportData.Location = new Point(452, 10);
            btnExportData.Name = "btnExportData";
            btnExportData.Size = new Size(101, 37);
            btnExportData.TabIndex = 2;
            btnExportData.Text = "导出数据";
            btnExportData.UseVisualStyleBackColor = false;
            btnExportData.Click += btnExportData_Click;
            // 
            // btnImportData
            // 
            btnImportData.BackColor = Color.FromArgb(0, 120, 215);
            btnImportData.FlatStyle = FlatStyle.Flat;
            btnImportData.Font = new Font("微软雅黑", 9F);
            btnImportData.ForeColor = Color.White;
            btnImportData.Location = new Point(223, 13);
            btnImportData.Name = "btnImportData";
            btnImportData.Size = new Size(80, 30);
            btnImportData.TabIndex = 1;
            btnImportData.Text = "导入数据";
            btnImportData.UseVisualStyleBackColor = false;
            btnImportData.Click += btnImportData_Click;
            // 
            // btnClearData
            // 
            btnClearData.BackColor = Color.FromArgb(60, 60, 60);
            btnClearData.FlatStyle = FlatStyle.Flat;
            btnClearData.Font = new Font("微软雅黑", 9F);
            btnClearData.ForeColor = Color.White;
            btnClearData.Location = new Point(20, 10);
            btnClearData.Name = "btnClearData";
            btnClearData.Size = new Size(80, 30);
            btnClearData.TabIndex = 0;
            btnClearData.Text = "清空数据";
            btnClearData.UseVisualStyleBackColor = false;
            btnClearData.Click += btnClearData_Click;
            // 
            // pnlChart
            // 
            pnlChart.BackColor = Color.FromArgb(33, 33, 33);
            pnlChart.Dock = DockStyle.Fill;
            pnlChart.Location = new Point(600, 180);
            pnlChart.Name = "pnlChart";
            pnlChart.Size = new Size(815, 589);
            pnlChart.TabIndex = 3;
            // 
            // StaticRockMechanicsForm
            // 
            AutoScaleDimensions = new SizeF(11F, 24F);
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = Color.FromArgb(33, 33, 33);
            ClientSize = new Size(1415, 769);
            Controls.Add(pnlChart);
            Controls.Add(pnlData);
            Controls.Add(pnlInput);
            Controls.Add(lblTitle);
            Font = new Font("微软雅黑", 9F);
            ForeColor = Color.White;
            MinimumSize = new Size(1200, 600);
            Name = "StaticRockMechanicsForm";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "静态岩石力学参数分析";
            FormClosing += StaticRockMechanicsForm_FormClosing;
            pnlInput.ResumeLayout(false);
            pnlInput.PerformLayout();
            pnlData.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)dgvMechanicsData).EndInit();
            pnlDataButtons.ResumeLayout(false);
            ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Label lblTitle;
        private System.Windows.Forms.Panel pnlInput;
        private System.Windows.Forms.Button btnBack;
        private System.Windows.Forms.Label lblCalculationResult;
        private System.Windows.Forms.Button btnCalculate;
        private System.Windows.Forms.TextBox txtVs;
        private System.Windows.Forms.TextBox txtVp;
        private System.Windows.Forms.TextBox txtDensity;
        private System.Windows.Forms.Label lblVs;
        private System.Windows.Forms.Label lblVp;
        private System.Windows.Forms.Label lblDensity;
        private System.Windows.Forms.Panel pnlData;
        private System.Windows.Forms.DataGridView dgvMechanicsData;
        private System.Windows.Forms.Panel pnlDataButtons;
        private System.Windows.Forms.Button btnExportData;
        private System.Windows.Forms.Button btnImportData;
        private System.Windows.Forms.Button btnClearData;
        private System.Windows.Forms.Panel pnlChart;
        private System.Windows.Forms.DataVisualization.Charting.Chart chartBrittleness;
    }
}
