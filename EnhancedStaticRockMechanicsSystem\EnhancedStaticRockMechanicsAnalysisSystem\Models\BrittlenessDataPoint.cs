using System;

namespace EnhancedStaticRockMechanicsAnalysisSystem.Models
{
    /// <summary>
    /// 脆性指数数据点
    /// </summary>
    public class BrittlenessDataPoint
    {
        /// <summary>
        /// 数据点ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// X坐标值
        /// </summary>
        public double X { get; set; }

        /// <summary>
        /// Y坐标值（脆性指数）
        /// </summary>
        public double Y { get; set; }

        /// <summary>
        /// 数据点标签
        /// </summary>
        public string? Label { get; set; }

        /// <summary>
        /// 数据点颜色
        /// </summary>
        public string? Color { get; set; }

        /// <summary>
        /// 是否选中
        /// </summary>
        public bool IsSelected { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 关联的岩石力学数据点
        /// </summary>
        public RockMechanicsDataPoint? RockMechanicsData { get; set; }

        public BrittlenessDataPoint()
        {
        }

        public BrittlenessDataPoint(double x, double y)
        {
            X = x;
            Y = y;
        }

        public BrittlenessDataPoint(double x, double y, string label) : this(x, y)
        {
            Label = label;
        }

        /// <summary>
        /// 获取数据点的字符串表示
        /// </summary>
        /// <returns></returns>
        public override string ToString()
        {
            return $"({X:F2}, {Y:F3})" + (string.IsNullOrEmpty(Label) ? "" : $" - {Label}");
        }

        /// <summary>
        /// 计算与另一个数据点的距离
        /// </summary>
        /// <param name="other"></param>
        /// <returns></returns>
        public double DistanceTo(BrittlenessDataPoint other)
        {
            return Math.Sqrt(Math.Pow(X - other.X, 2) + Math.Pow(Y - other.Y, 2));
        }

        /// <summary>
        /// 复制数据点
        /// </summary>
        /// <returns></returns>
        public BrittlenessDataPoint Clone()
        {
            return new BrittlenessDataPoint(X, Y, Label)
            {
                Id = Id,
                Color = Color,
                IsSelected = IsSelected,
                CreatedTime = CreatedTime,
                RockMechanicsData = RockMechanicsData
            };
        }
    }
}
