#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
人机验证处理模块
专门处理Web of Science等网站的人机验证
"""

import time
import random
from typing import Optional
from loguru import logger

try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.common.action_chains import ActionChains
    import pyautogui
except ImportError as e:
    logger.error(f"依赖库导入失败: {e}")


class CaptchaHandler:
    """人机验证处理器"""
    
    def __init__(self, driver):
        self.driver = driver
        # 设置pyautogui安全设置
        try:
            pyautogui.FAILSAFE = True
            pyautogui.PAUSE = 0.1
        except:
            pass
    
    def detect_captcha(self) -> bool:
        """检测是否存在人机验证"""
        captcha_patterns = [
            # reCAPTCHA
            'iframe[src*="recaptcha"]',
            'div[class*="g-recaptcha"]',
            'div[id*="recaptcha"]',
            
            # hCaptcha
            'iframe[src*="hcaptcha"]',
            'div[class*="h-captcha"]',
            
            # Cloudflare
            'div[class*="cf-browser-verification"]',
            'div[class*="cf-challenge"]',
            'div[id*="cf-wrapper"]',
            
            # 通用验证码
            'div[class*="captcha"]',
            'div[id*="captcha"]',
            'iframe[title*="captcha"]',
            
            # 中文验证提示
            'div:contains("人机验证")',
            'div:contains("安全验证")',
            'div:contains("请完成验证")',
            
            # 英文验证提示
            'div:contains("Please verify")',
            'div:contains("Verification required")',
            'div:contains("Security check")',
            
            # 验证按钮
            'button:contains("验证")',
            'button:contains("Verify")',
            'input[value*="验证"]'
        ]
        
        for pattern in captcha_patterns:
            try:
                elements = self.driver.find_elements(By.CSS_SELECTOR, pattern)
                if elements and any(elem.is_displayed() for elem in elements):
                    logger.info(f"检测到验证码: {pattern}")
                    return True
            except:
                continue
        
        # 检查页面URL和标题
        try:
            url = self.driver.current_url.lower()
            title = self.driver.title.lower()
            
            captcha_keywords = ['captcha', 'verification', 'challenge', '验证', '人机']
            
            if any(keyword in url or keyword in title for keyword in captcha_keywords):
                logger.info(f"URL或标题包含验证关键词: {url}, {title}")
                return True
        except:
            pass
        
        return False
    
    def handle_captcha(self) -> bool:
        """处理人机验证"""
        logger.info("开始处理人机验证...")

        # 检查验证码类型
        captcha_type = self._identify_captcha_type()
        logger.info(f"识别到验证码类型: {captcha_type}")

        # 根据类型选择处理策略
        if captcha_type == "image_selection":
            logger.info("检测到图像选择验证码，需要用户手动处理")
            return self._handle_image_captcha()
        elif captcha_type == "simple_click":
            # 策略1: 尝试自动点击
            if self._try_auto_click():
                return True

        # 策略2: 使用鼠标控制
        if self._try_mouse_control():
            return True

        # 策略3: 等待用户手动处理
        return self._wait_user_manual()
    
    def _try_auto_click(self) -> bool:
        """尝试自动点击验证元素"""
        click_selectors = [
            # reCAPTCHA复选框
            'div[class*="recaptcha-checkbox-border"]',
            'span[class*="recaptcha-checkbox"]',
            
            # hCaptcha复选框
            'div[class*="hcaptcha-checkbox"]',
            
            # 通用验证按钮
            'button[class*="verify"]',
            'input[type="checkbox"][class*="captcha"]',
            'div[role="checkbox"]',
            
            # Cloudflare验证
            'input[type="checkbox"][name="cf_captcha_kind"]',
            'button[type="submit"][value="Verify"]'
        ]
        
        for selector in click_selectors:
            try:
                element = WebDriverWait(self.driver, 3).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                )
                
                if element.is_displayed():
                    logger.info(f"尝试点击验证元素: {selector}")
                    self._human_click(element)
                    
                    # 等待验证结果
                    time.sleep(3)
                    
                    # 检查是否验证成功
                    if not self.detect_captcha():
                        logger.info("自动点击验证成功")
                        return True
                        
            except Exception as e:
                logger.debug(f"点击 {selector} 失败: {e}")
                continue
        
        return False
    
    def _human_click(self, element):
        """模拟人类点击"""
        try:
            # 滚动到元素
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
            time.sleep(random.uniform(0.3, 0.8))
            
            # 获取元素位置
            location = element.location_once_scrolled_into_view
            size = element.size
            
            # 计算点击位置（元素中心附近的随机位置）
            x = location['x'] + size['width'] // 2 + random.randint(-5, 5)
            y = location['y'] + size['height'] // 2 + random.randint(-5, 5)
            
            # 使用ActionChains模拟鼠标移动
            actions = ActionChains(self.driver)
            actions.move_to_element_with_offset(element, 
                                              random.randint(-2, 2), 
                                              random.randint(-2, 2))
            actions.pause(random.uniform(0.1, 0.3))
            actions.click()
            actions.perform()
            
        except Exception as e:
            logger.debug(f"ActionChains点击失败: {e}")
            # 备用方案：JavaScript点击
            try:
                self.driver.execute_script("arguments[0].click();", element)
            except Exception as e2:
                logger.debug(f"JavaScript点击也失败: {e2}")
    
    def _try_mouse_control(self) -> bool:
        """尝试使用系统鼠标控制"""
        try:
            # 查找验证码iframe
            iframes = self.driver.find_elements(By.TAG_NAME, "iframe")
            
            for iframe in iframes:
                src = iframe.get_attribute("src") or ""
                if any(keyword in src.lower() for keyword in ['captcha', 'recaptcha', 'hcaptcha']):
                    logger.info("找到验证码iframe，尝试鼠标控制")
                    
                    # 获取iframe位置
                    location = iframe.location_once_scrolled_into_view
                    size = iframe.size
                    
                    # 计算点击位置
                    click_x = location['x'] + 30  # 复选框通常在左上角
                    click_y = location['y'] + 30
                    
                    # 获取浏览器窗口位置
                    window_pos = self.driver.get_window_position()
                    
                    # 计算屏幕坐标
                    screen_x = window_pos['x'] + click_x
                    screen_y = window_pos['y'] + click_y + 100  # 加上浏览器标题栏高度
                    
                    # 使用pyautogui点击
                    pyautogui.click(screen_x, screen_y)
                    time.sleep(2)
                    
                    # 检查是否成功
                    if not self.detect_captcha():
                        logger.info("鼠标控制验证成功")
                        return True
                    
        except Exception as e:
            logger.debug(f"鼠标控制失败: {e}")
        
        return False
    
    def _wait_user_manual(self) -> bool:
        """等待用户手动处理"""
        import tkinter as tk
        from tkinter import messagebox
        
        try:
            root = tk.Tk()
            root.withdraw()
            root.attributes('-topmost', True)  # 置顶显示
            
            result = messagebox.askokcancel(
                "🤖 人机验证处理",
                "检测到人机验证页面！\n\n"
                "🔧 自动处理方案已尝试但未成功\n"
                "👆 请在浏览器中手动完成验证\n\n"
                "完成验证后请点击'确定'继续\n"
                "如果无法完成验证，点击'取消'跳过\n\n"
                "⚠️ 请保持浏览器窗口打开",
                icon='question'
            )
            
            root.destroy()
            
            if result:
                # 给用户一些时间完成验证
                time.sleep(2)
                
                # 再次检查验证状态
                max_wait = 30  # 最多等待30秒
                for i in range(max_wait):
                    if not self.detect_captcha():
                        logger.info("用户手动验证完成")
                        return True
                    time.sleep(1)
                
                logger.warning("验证状态未改变，但继续尝试")
                return True
            else:
                logger.info("用户选择跳过验证")
                return False
                
        except Exception as e:
            logger.error(f"用户交互失败: {e}")
            return False
    
    def wait_for_page_load(self, timeout: int = 10) -> bool:
        """等待页面加载完成"""
        try:
            WebDriverWait(self.driver, timeout).until(
                lambda driver: driver.execute_script("return document.readyState") == "complete"
            )
            return True
        except:
            return False

    def _identify_captcha_type(self) -> str:
        """识别验证码类型"""
        try:
            # 检查是否有图像选择验证码的特征
            image_indicators = [
                'img[src*="captcha"]',
                'div[class*="captcha-image"]',
                'div[class*="image-challenge"]',
                'canvas[class*="captcha"]',
                # 检查是否有多个可点击的图像
                'div[class*="captcha"] img',
                'div[role="button"] img'
            ]

            image_count = 0
            for indicator in image_indicators:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, indicator)
                    image_count += len(elements)
                except:
                    continue

            # 如果有多个图像，很可能是图像选择验证码
            if image_count >= 6:  # 通常图像验证码有9个或更多图像
                return "image_selection"

            # 检查页面文本内容
            try:
                page_text = self.driver.page_source.lower()
                if any(keyword in page_text for keyword in [
                    '选择', 'select', 'click', '点击', 'choose',
                    '图片', 'image', 'picture', '物体', 'object'
                ]):
                    return "image_selection"
            except:
                pass

            # 默认为简单点击验证
            return "simple_click"

        except Exception as e:
            logger.debug(f"识别验证码类型失败: {e}")
            return "unknown"

    def _handle_image_captcha(self) -> bool:
        """处理图像选择验证码"""
        try:
            # 截图保存验证码图像
            screenshot_path = self._save_captcha_screenshot()

            # 创建增强的用户交互界面
            return self._show_image_captcha_helper(screenshot_path)

        except Exception as e:
            logger.error(f"处理图像验证码失败: {e}")
            return self._wait_user_manual()

    def _save_captcha_screenshot(self) -> str:
        """保存验证码截图"""
        try:
            import os
            import time

            # 创建截图目录
            screenshot_dir = os.path.join(os.getcwd(), "captcha_screenshots")
            os.makedirs(screenshot_dir, exist_ok=True)

            # 生成文件名
            timestamp = int(time.time())
            screenshot_path = os.path.join(screenshot_dir, f"captcha_{timestamp}.png")

            # 截图
            self.driver.save_screenshot(screenshot_path)
            logger.info(f"验证码截图已保存: {screenshot_path}")

            return screenshot_path

        except Exception as e:
            logger.error(f"保存截图失败: {e}")
            return ""

    def _show_image_captcha_helper(self, screenshot_path: str) -> bool:
        """显示图像验证码辅助界面"""
        try:
            # 尝试使用新的图像验证码辅助工具
            try:
                from ..utils.image_captcha_helper import show_image_captcha_dialog

                # 获取验证码提示文本
                captcha_text = self._extract_captcha_text()

                # 显示辅助界面
                result = show_image_captcha_dialog(screenshot_path, captcha_text)

                if result:
                    # 等待验证完成
                    return self._wait_for_verification_complete()
                else:
                    logger.info("用户选择跳过图像验证")
                    return False

            except ImportError:
                logger.warning("无法导入图像验证码辅助工具，使用简化界面")
                return self._show_simple_captcha_dialog(screenshot_path)

        except Exception as e:
            logger.error(f"显示图像验证码辅助界面失败: {e}")
            return self._wait_user_manual()

    def _extract_captcha_text(self) -> str:
        """提取验证码提示文本"""
        try:
            # 尝试从页面中提取验证码提示文本
            text_selectors = [
                'div[class*="captcha"] p',
                'div[class*="challenge"] p',
                'div[class*="instruction"]',
                '.captcha-instruction',
                '.challenge-text'
            ]

            for selector in text_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        text = element.text.strip()
                        if text and len(text) > 10:  # 有意义的文本
                            return text
                except:
                    continue

            return ""

        except Exception as e:
            logger.debug(f"提取验证码文本失败: {e}")
            return ""

    def _show_simple_captcha_dialog(self, screenshot_path: str) -> bool:
        """显示简化的验证码对话框"""
        import tkinter as tk
        from tkinter import messagebox
        import subprocess
        import os

        try:
            root = tk.Tk()
            root.withdraw()
            root.attributes('-topmost', True)

            # 构建详细的提示信息
            message = """🖼️ 检测到图像选择验证码！

这是一个需要识别图像内容的复杂验证码。

📋 处理步骤：
1. 仔细阅读验证码的提示文字
2. 根据提示选择正确的图像
3. 点击所有符合要求的图像
4. 点击验证按钮完成验证

💡 提示：
- 当前验证要求选择"引擎才能工作的物体"
- 通常包括：汽车、摩托车、船只、飞机等
- 手机、电脑等电子设备通常不算

⚠️ 注意：
- 请在浏览器中手动完成验证
- 完成后点击下方"验证完成"按钮
- 如果验证失败可以点击刷新重试

是否已完成验证？"""

            # 如果截图存在，尝试打开
            if screenshot_path and os.path.exists(screenshot_path):
                try:
                    if os.name == 'nt':  # Windows
                        os.startfile(screenshot_path)
                    else:  # Linux/Mac
                        subprocess.run(['xdg-open', screenshot_path])
                    message = f"📸 验证码截图已打开\n\n{message}"
                except:
                    pass

            result = messagebox.askokcancel(
                "🖼️ 图像验证码处理",
                message,
                icon='question'
            )

            root.destroy()

            if result:
                # 等待验证完成
                return self._wait_for_verification_complete()
            else:
                logger.info("用户选择跳过图像验证")
                return False

        except Exception as e:
            logger.error(f"显示简化验证码对话框失败: {e}")
            return self._wait_user_manual()

    def _wait_for_verification_complete(self) -> bool:
        """等待验证完成"""
        logger.info("等待用户完成图像验证...")

        # 等待最多60秒
        max_wait = 60
        for i in range(max_wait):
            time.sleep(1)

            # 检查是否还有验证码
            if not self.detect_captcha():
                logger.info("图像验证完成")
                return True

            # 每10秒检查一次页面变化
            if i % 10 == 0 and i > 0:
                try:
                    # 检查是否有成功提示或页面跳转
                    current_url = self.driver.current_url
                    if 'captcha' not in current_url.lower() and 'verify' not in current_url.lower():
                        logger.info("页面已跳转，验证可能完成")
                        return True
                except:
                    pass

        logger.warning("等待验证超时，但继续尝试")
        return True
