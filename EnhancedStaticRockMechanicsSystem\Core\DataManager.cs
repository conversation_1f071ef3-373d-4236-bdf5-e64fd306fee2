using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;

namespace EnhancedStaticRockMechanicsSystem.Core
{
    /// <summary>
    /// 数据管理器类，负责数据的加载、保存和处理
    /// </summary>
    public class DataManager
    {
        #region 私有字段

        private DataTable _sourceData;
        private readonly Dictionary<string, string> _columnMappings;
        private string _lastLoadedFilePath;

        #endregion

        #region 构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public DataManager()
        {
            _sourceData = new DataTable();
            _columnMappings = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
            _lastLoadedFilePath = string.Empty;
        }

        /// <summary>
        /// 带数据的构造函数
        /// </summary>
        /// <param name="data">数据表</param>
        public DataManager(DataTable data)
        {
            _sourceData = data?.Copy() ?? new DataTable();
            _columnMappings = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
            _lastLoadedFilePath = string.Empty;
        }

        #endregion

        #region 属性

        /// <summary>
        /// 获取或设置源数据
        /// </summary>
        public DataTable SourceData => _sourceData;

        /// <summary>
        /// 获取列名映射
        /// </summary>
        public Dictionary<string, string> ColumnMappings => _columnMappings;

        /// <summary>
        /// 获取最后加载的文件路径
        /// </summary>
        public string LastLoadedFilePath => _lastLoadedFilePath;

        #endregion

        #region 公共方法

        /// <summary>
        /// 从Excel文件加载数据
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>成功返回true，失败返回false</returns>
        public bool LoadFromExcel(string filePath)
        {
            if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
                return false;

            try
            {
                IWorkbook workbook = null;
                FileStream fs = new FileStream(filePath, FileMode.Open, FileAccess.Read);

                // 根据文件扩展名确定Excel版本
                if (filePath.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase))
                {
                    workbook = new XSSFWorkbook(fs);
                }
                else if (filePath.EndsWith(".xls", StringComparison.OrdinalIgnoreCase))
                {
                    workbook = new HSSFWorkbook(fs);
                }
                else
                {
                    fs.Close();
                    return false;
                }

                // 获取第一个工作表
                ISheet sheet = workbook.GetSheetAt(0);
                if (sheet == null)
                {
                    fs.Close();
                    workbook.Close();
                    return false;
                }

                // 创建数据表并设置列
                DataTable dataTable = new DataTable();
                IRow headerRow = sheet.GetRow(0);
                if (headerRow == null)
                {
                    fs.Close();
                    workbook.Close();
                    return false;
                }

                // 清空之前的列名映射
                _columnMappings.Clear();

                // 添加列
                int cellCount = headerRow.LastCellNum;
                for (int i = 0; i < cellCount; i++)
                {
                    ICell cell = headerRow.GetCell(i);
                    string originalColumnName = "";
                    string finalColumnName = "";

                    if (cell != null)
                    {
                        originalColumnName = cell.ToString();
                        if (string.IsNullOrEmpty(originalColumnName))
                        {
                            originalColumnName = $"Column{i}";
                            finalColumnName = $"Column{i}";
                        }
                        else
                        {
                            finalColumnName = originalColumnName;

                            // 确保列名唯一
                            if (dataTable.Columns.Contains(finalColumnName))
                                finalColumnName = $"{originalColumnName}_{i}";
                        }

                        dataTable.Columns.Add(finalColumnName);

                        // 保存原始列名到最终列名的映射
                        if (!string.IsNullOrEmpty(originalColumnName) && originalColumnName != finalColumnName)
                        {
                            _columnMappings[originalColumnName] = finalColumnName;
                        }
                    }
                    else
                    {
                        finalColumnName = $"Column{i}";
                        dataTable.Columns.Add(finalColumnName);
                    }
                }

                // 添加数据行
                for (int i = 1; i <= sheet.LastRowNum; i++)
                {
                    IRow row = sheet.GetRow(i);
                    if (row == null)
                        continue;

                    DataRow dataRow = dataTable.NewRow();
                    for (int j = 0; j < cellCount; j++)
                    {
                        ICell cell = row.GetCell(j);
                        if (cell != null)
                        {
                            // 根据单元格类型获取值
                            switch (cell.CellType)
                            {
                                case CellType.Numeric:
                                    if (DateUtil.IsCellDateFormatted(cell))
                                    {
                                        dataRow[j] = cell.DateCellValue;
                                    }
                                    else
                                    {
                                        dataRow[j] = cell.NumericCellValue;
                                    }
                                    break;
                                case CellType.String:
                                    dataRow[j] = cell.StringCellValue;
                                    break;
                                case CellType.Boolean:
                                    dataRow[j] = cell.BooleanCellValue;
                                    break;
                                case CellType.Formula:
                                    // 对于公式单元格，获取计算结果
                                    switch (cell.CachedFormulaResultType)
                                    {
                                        case CellType.Numeric:
                                            dataRow[j] = cell.NumericCellValue;
                                            break;
                                        case CellType.String:
                                            dataRow[j] = cell.StringCellValue;
                                            break;
                                        default:
                                            dataRow[j] = cell.ToString();
                                            break;
                                    }
                                    break;
                                default:
                                    dataRow[j] = cell.ToString();
                                    break;
                            }
                        }
                    }
                    dataTable.Rows.Add(dataRow);
                }

                fs.Close();
                workbook.Close();

                _sourceData = dataTable;
                _lastLoadedFilePath = filePath;
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"加载Excel文件失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取列名列表
        /// </summary>
        /// <returns>列名列表</returns>
        public List<string> GetColumnNames()
        {
            return _sourceData.Columns.Cast<DataColumn>().Select(column => column.ColumnName).ToList();
        }

        /// <summary>
        /// 获取数据行数
        /// </summary>
        /// <returns>数据行数</returns>
        public int GetRowCount()
        {
            return _sourceData.Rows.Count;
        }

        /// <summary>
        /// 获取数据列数
        /// </summary>
        /// <returns>数据列数</returns>
        public int GetColumnCount()
        {
            return _sourceData.Columns.Count;
        }

        /// <summary>
        /// 清空数据
        /// </summary>
        public void Clear()
        {
            _sourceData.Clear();
            _columnMappings.Clear();
            _lastLoadedFilePath = string.Empty;
        }

        #endregion
    }
}
