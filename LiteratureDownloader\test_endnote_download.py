#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试EndNote引用下载功能
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_endnote_download():
    """测试EndNote引用下载"""
    print("测试EndNote引用下载功能")
    print("=" * 50)
    
    try:
        from modules.auto_downloader import AutoDownloader
        
        # 创建测试下载目录
        download_dir = Path("endnote_downloads")
        download_dir.mkdir(exist_ok=True)
        
        print(f"下载目录: {download_dir.absolute()}")
        
        # 创建自动下载器
        auto_downloader = AutoDownloader(str(download_dir))
        print("✓ 自动下载器创建成功")
        
        # 创建测试文献数据
        test_citations = [
            {
                'reference_number': '1',
                'title': 'Machine learning in healthcare',
                'authors': '<PERSON>, <PERSON><PERSON>',
                'year': '2023',
                'journal': 'Nature'
            }
        ]
        
        print(f"\n测试文献:")
        for citation in test_citations:
            ref_num = citation['reference_number']
            title = citation['title']
            authors = citation['authors']
            print(f"  [{ref_num}] {title} - {authors}")
        
        # 询问是否执行实际下载测试
        print(f"\n注意: 这将打开浏览器并访问Web of Science")
        print(f"确保您有网络连接和访问权限")
        
        choice = input("\n是否执行EndNote下载测试? (y/N): ").strip().lower()
        
        if choice == 'y':
            print("\n开始EndNote下载测试...")
            
            # 创建WebDriver
            driver = auto_downloader.setup_driver()
            if not driver:
                print("✗ WebDriver创建失败")
                return
            
            print("✓ WebDriver创建成功")
            
            try:
                # 测试单个文献下载
                citation = test_citations[0]
                success = auto_downloader.search_and_download_citation(driver, citation)
                
                if success:
                    print("✓ EndNote引用下载成功")
                else:
                    print("✗ EndNote引用下载失败")
                
                # 等待用户查看结果
                input("\n请检查浏览器和下载目录，按回车键继续...")
                
                # 检查下载的文件
                downloaded_files = list(download_dir.glob("*"))
                print(f"\n下载的文件:")
                for file in downloaded_files:
                    print(f"  {file.name}")
                
            finally:
                driver.quit()
                print("✓ 浏览器已关闭")
        else:
            print("跳过实际下载测试")
        
        print("\n✓ 测试完成")
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_webdriver_only():
    """仅测试WebDriver创建"""
    print("测试WebDriver创建")
    print("=" * 30)
    
    try:
        from modules.auto_downloader import AutoDownloader
        
        download_dir = Path("test_downloads")
        auto_downloader = AutoDownloader(str(download_dir))
        
        print("创建WebDriver实例...")
        driver = auto_downloader.setup_driver()
        
        if driver:
            print("✓ WebDriver创建成功")
            
            # 测试打开Web of Science
            print("测试打开Web of Science...")
            driver.get("https://webofscience.clarivate.cn")
            time.sleep(3)
            
            print("✓ 成功访问Web of Science")
            print("请检查浏览器窗口是否正常显示")
            
            input("按回车键关闭浏览器...")
            driver.quit()
            print("✓ WebDriver已关闭")
        else:
            print("✗ WebDriver创建失败")
            
    except Exception as e:
        print(f"✗ WebDriver测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("选择测试模式:")
    print("1. 仅测试WebDriver创建")
    print("2. 测试完整EndNote下载")
    
    try:
        choice = input("请输入选择 (1 或 2): ").strip()
        
        if choice == "1":
            test_webdriver_only()
        elif choice == "2":
            test_endnote_download()
        else:
            print("无效选择")
            
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试过程中出错: {e}")
    
    input("\n按回车键退出...")  # 防止窗口闪退
