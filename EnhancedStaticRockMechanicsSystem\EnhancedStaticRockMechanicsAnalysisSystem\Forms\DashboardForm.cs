using System;
using System.Drawing;
using System.Windows.Forms;
using EnhancedStaticRockMechanicsAnalysisSystem.Services;

namespace EnhancedStaticRockMechanicsAnalysisSystem.Forms
{
    public partial class DashboardForm : Form
    {
        private string currentUser;

        public DashboardForm(string username)
        {
            InitializeComponent();
            currentUser = username;
            this.Text = $"增强版静态岩石力学参数分析系统 - 用户: {username}";
            this.WindowState = FormWindowState.Maximized;
            
            // 记录用户登录
            LoggingService.Instance.Info($"用户 {username} 进入主界面");
        }

        private void btnStaticRockMechanics_Click(object sender, EventArgs e)
        {
            try
            {
                // 打开静态岩石力学参数分析窗体
                StaticRockMechanicsForm staticForm = new StaticRockMechanicsForm();
                this.Hide();
                DialogResult result = staticForm.ShowDialog();
                if (result == DialogResult.OK)
                {
                    this.Show();
                }
                else
                {
                    this.Show();
                }
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"打开静态岩石力学参数分析窗体失败: {ex.Message}");
                MessageBox.Show($"打开窗体失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnLogout_Click(object sender, EventArgs e)
        {
            DialogResult result = MessageBox.Show("确定要退出登录吗？", "确认退出", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            
            if (result == DialogResult.Yes)
            {
                LoggingService.Instance.Info($"用户 {currentUser} 退出登录");
                this.Close();
            }
        }

        private void btnExit_Click(object sender, EventArgs e)
        {
            DialogResult result = MessageBox.Show("确定要退出系统吗？", "确认退出", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            
            if (result == DialogResult.Yes)
            {
                LoggingService.Instance.Info($"用户 {currentUser} 退出系统");
                Application.Exit();
            }
        }

        private void DashboardForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (e.CloseReason == CloseReason.UserClosing)
            {
                DialogResult result = MessageBox.Show("确定要退出系统吗？", "确认退出", 
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                
                if (result == DialogResult.No)
                {
                    e.Cancel = true;
                    return;
                }
            }
            
            LoggingService.Instance.Info($"用户 {currentUser} 关闭主界面");
        }
    }
}
