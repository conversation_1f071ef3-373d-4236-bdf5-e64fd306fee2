namespace EnhancedStaticRockMechanicsSystem.Forms
{
    partial class EnhancedComparisonChartForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.Windows.Forms.DataVisualization.Charting.ChartArea chartArea1 = new System.Windows.Forms.DataVisualization.Charting.ChartArea();
            System.Windows.Forms.DataVisualization.Charting.Legend legend1 = new System.Windows.Forms.DataVisualization.Charting.Legend();
            this.lblTitle = new System.Windows.Forms.Label();
            this.pnlMain = new System.Windows.Forms.Panel();
            this.chartComparison = new System.Windows.Forms.DataVisualization.Charting.Chart();
            this.splitterImageChart = new System.Windows.Forms.Splitter();
            this.pnlImageViewer = new System.Windows.Forms.Panel();
            this.picImageViewer = new System.Windows.Forms.PictureBox();
            this.cmbImages = new System.Windows.Forms.ComboBox();
            this.pnlControls = new System.Windows.Forms.Panel();
            this.btnToggleImageView = new System.Windows.Forms.Button();
            this.btnRestore = new System.Windows.Forms.Button();
            this.btnSeparate = new System.Windows.Forms.Button();
            this.btnSaveImage = new System.Windows.Forms.Button();
            this.btnClose = new System.Windows.Forms.Button();
            this.pnlMain.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartComparison)).BeginInit();
            this.pnlImageViewer.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.picImageViewer)).BeginInit();
            this.pnlControls.SuspendLayout();
            this.SuspendLayout();
            //
            // lblTitle
            //
            this.lblTitle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(45)))), ((int)(((byte)(45)))));
            this.lblTitle.Dock = System.Windows.Forms.DockStyle.Top;
            this.lblTitle.Font = new System.Drawing.Font("微软雅黑", 16F, System.Drawing.FontStyle.Bold);
            this.lblTitle.ForeColor = System.Drawing.Color.White;
            this.lblTitle.Location = new System.Drawing.Point(0, 0);
            this.lblTitle.Name = "lblTitle";
            this.lblTitle.Size = new System.Drawing.Size(1400, 60);
            this.lblTitle.TabIndex = 0;
            this.lblTitle.Text = "增强版脆性指数计算方法对比图";
            this.lblTitle.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            //
            // pnlMain
            //
            this.pnlMain.Controls.Add(this.chartComparison);
            this.pnlMain.Controls.Add(this.splitterImageChart);
            this.pnlMain.Controls.Add(this.pnlImageViewer);
            this.pnlMain.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlMain.Location = new System.Drawing.Point(0, 60);
            this.pnlMain.Name = "pnlMain";
            this.pnlMain.Size = new System.Drawing.Size(1400, 780);
            this.pnlMain.TabIndex = 1;
            //
            // chartComparison
            //
            this.chartComparison.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(33)))), ((int)(((byte)(33)))), ((int)(((byte)(33)))));
            chartArea1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(33)))), ((int)(((byte)(33)))), ((int)(((byte)(33)))));
            chartArea1.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(60)))), ((int)(((byte)(60)))));
            chartArea1.BorderWidth = 1;
            chartArea1.Name = "ChartArea1";
            this.chartComparison.ChartAreas.Add(chartArea1);
            this.chartComparison.Dock = System.Windows.Forms.DockStyle.Fill;
            legend1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(60)))), ((int)(((byte)(60)))));
            legend1.ForeColor = System.Drawing.Color.White;
            legend1.Name = "Legend1";
            this.chartComparison.Legends.Add(legend1);
            this.chartComparison.Location = new System.Drawing.Point(0, 0);
            this.chartComparison.Name = "chartComparison";
            this.chartComparison.Size = new System.Drawing.Size(997, 780);
            this.chartComparison.TabIndex = 0;
            this.chartComparison.Text = "对比图表";
            //
            // splitterImageChart
            //
            this.splitterImageChart.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(60)))), ((int)(((byte)(60)))));
            this.splitterImageChart.Dock = System.Windows.Forms.DockStyle.Right;
            this.splitterImageChart.Location = new System.Drawing.Point(997, 0);
            this.splitterImageChart.Name = "splitterImageChart";
            this.splitterImageChart.Size = new System.Drawing.Size(3, 780);
            this.splitterImageChart.TabIndex = 2;
            this.splitterImageChart.TabStop = false;
            this.splitterImageChart.Visible = false;
            //
            // pnlImageViewer
            //
            this.pnlImageViewer.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(45)))), ((int)(((byte)(45)))));
            this.pnlImageViewer.Controls.Add(this.picImageViewer);
            this.pnlImageViewer.Controls.Add(this.cmbImages);
            this.pnlImageViewer.Dock = System.Windows.Forms.DockStyle.Right;
            this.pnlImageViewer.Location = new System.Drawing.Point(1000, 0);
            this.pnlImageViewer.Name = "pnlImageViewer";
            this.pnlImageViewer.Size = new System.Drawing.Size(400, 780);
            this.pnlImageViewer.TabIndex = 1;
            this.pnlImageViewer.Visible = false;
            //
            // picImageViewer
            //
            this.picImageViewer.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(33)))), ((int)(((byte)(33)))), ((int)(((byte)(33)))));
            this.picImageViewer.Dock = System.Windows.Forms.DockStyle.Fill;
            this.picImageViewer.Location = new System.Drawing.Point(0, 30);
            this.picImageViewer.Name = "picImageViewer";
            this.picImageViewer.Size = new System.Drawing.Size(400, 750);
            this.picImageViewer.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom;
            this.picImageViewer.TabIndex = 1;
            this.picImageViewer.TabStop = false;
            //
            // cmbImages
            //
            this.cmbImages.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(60)))), ((int)(((byte)(60)))));
            this.cmbImages.Dock = System.Windows.Forms.DockStyle.Top;
            this.cmbImages.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbImages.ForeColor = System.Drawing.Color.White;
            this.cmbImages.FormattingEnabled = true;
            this.cmbImages.Location = new System.Drawing.Point(0, 0);
            this.cmbImages.Name = "cmbImages";
            this.cmbImages.Size = new System.Drawing.Size(400, 32);
            this.cmbImages.TabIndex = 0;
            //
            // pnlControls
            //
            this.pnlControls.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(45)))), ((int)(((byte)(45)))));
            this.pnlControls.Controls.Add(this.btnToggleImageView);
            this.pnlControls.Controls.Add(this.btnRestore);
            this.pnlControls.Controls.Add(this.btnSeparate);
            this.pnlControls.Controls.Add(this.btnSaveImage);
            this.pnlControls.Controls.Add(this.btnClose);
            this.pnlControls.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.pnlControls.Location = new System.Drawing.Point(0, 840);
            this.pnlControls.Name = "pnlControls";
            this.pnlControls.Size = new System.Drawing.Size(1400, 60);
            this.pnlControls.TabIndex = 2;
            //
            // btnToggleImageView
            //
            this.btnToggleImageView.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(60)))), ((int)(((byte)(60)))));
            this.btnToggleImageView.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnToggleImageView.ForeColor = System.Drawing.Color.White;
            this.btnToggleImageView.Location = new System.Drawing.Point(370, 12);
            this.btnToggleImageView.Name = "btnToggleImageView";
            this.btnToggleImageView.Size = new System.Drawing.Size(80, 35);
            this.btnToggleImageView.TabIndex = 4;
            this.btnToggleImageView.Text = "显示图片";
            this.btnToggleImageView.UseVisualStyleBackColor = false;
            //
            // btnRestore
            //
            this.btnRestore.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(60)))), ((int)(((byte)(60)))));
            this.btnRestore.Enabled = false;
            this.btnRestore.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnRestore.ForeColor = System.Drawing.Color.White;
            this.btnRestore.Location = new System.Drawing.Point(280, 12);
            this.btnRestore.Name = "btnRestore";
            this.btnRestore.Size = new System.Drawing.Size(80, 35);
            this.btnRestore.TabIndex = 3;
            this.btnRestore.Text = "合并显示";
            this.btnRestore.UseVisualStyleBackColor = false;
            //
            // btnSeparate
            //
            this.btnSeparate.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(60)))), ((int)(((byte)(60)))));
            this.btnSeparate.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnSeparate.ForeColor = System.Drawing.Color.White;
            this.btnSeparate.Location = new System.Drawing.Point(190, 12);
            this.btnSeparate.Name = "btnSeparate";
            this.btnSeparate.Size = new System.Drawing.Size(80, 35);
            this.btnSeparate.TabIndex = 2;
            this.btnSeparate.Text = "分隔显示";
            this.btnSeparate.UseVisualStyleBackColor = false;
            //
            // btnSaveImage
            //
            this.btnSaveImage.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(60)))), ((int)(((byte)(60)))));
            this.btnSaveImage.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnSaveImage.ForeColor = System.Drawing.Color.White;
            this.btnSaveImage.Location = new System.Drawing.Point(100, 12);
            this.btnSaveImage.Name = "btnSaveImage";
            this.btnSaveImage.Size = new System.Drawing.Size(80, 35);
            this.btnSaveImage.TabIndex = 1;
            this.btnSaveImage.Text = "保存图片";
            this.btnSaveImage.UseVisualStyleBackColor = false;
            //
            // btnClose
            //
            this.btnClose.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(60)))), ((int)(((byte)(60)))));
            this.btnClose.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnClose.ForeColor = System.Drawing.Color.White;
            this.btnClose.Location = new System.Drawing.Point(10, 12);
            this.btnClose.Name = "btnClose";
            this.btnClose.Size = new System.Drawing.Size(80, 35);
            this.btnClose.TabIndex = 0;
            this.btnClose.Text = "关闭";
            this.btnClose.UseVisualStyleBackColor = false;
            //
            // EnhancedComparisonChartForm
            //
            this.AutoScaleDimensions = new System.Drawing.SizeF(11F, 24F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(33)))), ((int)(((byte)(33)))), ((int)(((byte)(33)))));
            this.ClientSize = new System.Drawing.Size(1400, 900);
            this.Controls.Add(this.pnlMain);
            this.Controls.Add(this.pnlControls);
            this.Controls.Add(this.lblTitle);
            this.Font = new System.Drawing.Font("微软雅黑", 9F);
            this.ForeColor = System.Drawing.Color.White;
            this.Name = "EnhancedComparisonChartForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "增强版脆性指数对比图";
            this.WindowState = System.Windows.Forms.FormWindowState.Maximized;
            this.pnlMain.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.chartComparison)).EndInit();
            this.pnlImageViewer.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.picImageViewer)).EndInit();
            this.pnlControls.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Label lblTitle;
        private System.Windows.Forms.Panel pnlMain;
        private System.Windows.Forms.DataVisualization.Charting.Chart chartComparison;
        private System.Windows.Forms.Splitter splitterImageChart;
        private System.Windows.Forms.Panel pnlImageViewer;
        private System.Windows.Forms.PictureBox picImageViewer;
        private System.Windows.Forms.ComboBox cmbImages;
        private System.Windows.Forms.Panel pnlControls;
        private System.Windows.Forms.Button btnToggleImageView;
        private System.Windows.Forms.Button btnRestore;
        private System.Windows.Forms.Button btnSeparate;
        private System.Windows.Forms.Button btnSaveImage;
        private System.Windows.Forms.Button btnClose;
    }
}
