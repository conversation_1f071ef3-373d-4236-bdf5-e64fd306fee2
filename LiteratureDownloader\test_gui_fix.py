#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GUI修复
"""

import sys
import tkinter as tk
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_gui():
    """测试GUI启动"""
    try:
        print("正在测试GUI启动...")
        
        from gui.main_window import LiteratureCitationApp
        
        print("✅ GUI模块导入成功")
        
        # 创建应用
        app = LiteratureCitationApp()
        print("✅ 应用创建成功")
        
        # 测试日志功能
        app.main_window.log_message("测试日志消息")
        print("✅ 日志功能正常")
        
        print("🎉 GUI测试通过！可以正常启动程序了")
        
        # 询问是否启动完整GUI
        if input("是否启动完整GUI界面？(y/n): ").lower() == 'y':
            app.run()
        
    except Exception as e:
        print(f"❌ GUI测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_gui()
