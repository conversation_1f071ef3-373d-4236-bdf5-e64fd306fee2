using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace EnhancedStaticRockMechanicsSystem.Models
{
    /// <summary>
    /// 计算结果模型类
    /// </summary>
    public class CalculationResult
    {
        /// <summary>
        /// 获取或设置地质ID
        /// </summary>
        public string GeoID { get; set; }

        /// <summary>
        /// 获取或设置顶深（米）
        /// </summary>
        public double TopDepth { get; set; }

        /// <summary>
        /// 获取或设置底深（米）
        /// </summary>
        public double BottomDepth { get; set; }

        /// <summary>
        /// 获取或设置脆性指数
        /// </summary>
        public double BrittlenessIndex { get; set; }

        /// <summary>
        /// 获取或设置脆性矿物总量
        /// </summary>
        public double BrittleMineralTotal { get; set; }

        /// <summary>
        /// 获取或设置塑性矿物总量
        /// </summary>
        public double DuctileMineralTotal { get; set; }

        /// <summary>
        /// 获取或设置原始数据列名及其值的字典
        /// </summary>
        public Dictionary<string, double> ColumnValues { get; set; } = new Dictionary<string, double>();

        /// <summary>
        /// 获取或设置结果分类级别(1-5)
        /// </summary>
        public int BrittlenessLevel { get; set; }

        /// <summary>
        /// 获取或设置结果分级描述
        /// </summary>
        public string LevelDescription { get; set; }

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public CalculationResult()
        {
            GeoID = string.Empty;
            TopDepth = 0;
            BottomDepth = 0;
            BrittlenessIndex = 0;
            BrittleMineralTotal = 0;
            DuctileMineralTotal = 0;
            BrittlenessLevel = 0;
            LevelDescription = string.Empty;
        }

        /// <summary>
        /// 带参数的构造函数
        /// </summary>
        /// <param name="geoId">地质ID</param>
        /// <param name="topDepth">顶深</param>
        /// <param name="bottomDepth">底深</param>
        /// <param name="brittlenessIndex">脆性指数</param>
        /// <param name="brittleMineralTotal">脆性矿物总量</param>
        /// <param name="ductileMineralTotal">塑性矿物总量</param>
        public CalculationResult(
            string geoId,
            double topDepth,
            double bottomDepth,
            double brittlenessIndex,
            double brittleMineralTotal,
            double ductileMineralTotal)
        {
            GeoID = geoId;
            TopDepth = topDepth;
            BottomDepth = bottomDepth;
            BrittlenessIndex = brittlenessIndex;
            BrittleMineralTotal = brittleMineralTotal;
            DuctileMineralTotal = ductileMineralTotal;
            DetermineBrittlenessLevel();
        }

        /// <summary>
        /// 添加列值
        /// </summary>
        /// <param name="columnName">列名</param>
        /// <param name="value">值</param>
        public void AddColumnValue(string columnName, double value)
        {
            if (!string.IsNullOrEmpty(columnName))
            {
                ColumnValues[columnName] = value;
            }
        }

        /// <summary>
        /// 获取列值
        /// </summary>
        /// <param name="columnName">列名</param>
        /// <param name="defaultValue">默认值（如果找不到列）</param>
        /// <returns>列值</returns>
        public double GetColumnValue(string columnName, double defaultValue = 0)
        {
            if (string.IsNullOrEmpty(columnName) || !ColumnValues.ContainsKey(columnName))
            {
                return defaultValue;
            }
            return ColumnValues[columnName];
        }

        /// <summary>
        /// 确定脆性指数级别及描述
        /// </summary>
        public void DetermineBrittlenessLevel()
        {
            if (BrittlenessIndex <= 0.2)
            {
                BrittlenessLevel = 1;
                LevelDescription = "极低脆性";
            }
            else if (BrittlenessIndex <= 0.4)
            {
                BrittlenessLevel = 2;
                LevelDescription = "低脆性";
            }
            else if (BrittlenessIndex <= 0.6)
            {
                BrittlenessLevel = 3;
                LevelDescription = "中等脆性";
            }
            else if (BrittlenessIndex <= 0.8)
            {
                BrittlenessLevel = 4;
                LevelDescription = "高脆性";
            }
            else
            {
                BrittlenessLevel = 5;
                LevelDescription = "极高脆性";
            }
        }

        /// <summary>
        /// 获取简短描述
        /// </summary>
        /// <returns>简短描述字符串</returns>
        public override string ToString()
        {
            return $"地质ID: {GeoID}, 深度: {TopDepth}-{BottomDepth}m, 脆性指数: {BrittlenessIndex:F2}, 级别: {LevelDescription}";
        }
    }
}
