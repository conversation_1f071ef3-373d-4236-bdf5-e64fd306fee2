namespace EnhancedStaticRockMechanicsSystem.Forms
{
    partial class BatchImportWizard
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.lblInstructions = new System.Windows.Forms.Label();
            this.lstFiles = new System.Windows.Forms.ListBox();
            this.pnlButtons = new System.Windows.Forms.Panel();
            this.btnPreview = new System.Windows.Forms.Button();
            this.btnRemoveFiles = new System.Windows.Forms.Button();
            this.btnAddFiles = new System.Windows.Forms.Button();
            this.dgvPreview = new System.Windows.Forms.DataGridView();
            this.progressBar = new System.Windows.Forms.ProgressBar();
            this.pnlBottomButtons = new System.Windows.Forms.Panel();
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnOK = new System.Windows.Forms.Button();
            this.pnlButtons.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvPreview)).BeginInit();
            this.pnlBottomButtons.SuspendLayout();
            this.SuspendLayout();
            //
            // lblInstructions
            //
            this.lblInstructions.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(33)))), ((int)(((byte)(33)))), ((int)(((byte)(33)))));
            this.lblInstructions.Font = new System.Drawing.Font("微软雅黑", 9F);
            this.lblInstructions.ForeColor = System.Drawing.Color.LightGray;
            this.lblInstructions.Location = new System.Drawing.Point(10, 10);
            this.lblInstructions.Name = "lblInstructions";
            this.lblInstructions.Size = new System.Drawing.Size(860, 40);
            this.lblInstructions.TabIndex = 0;
            this.lblInstructions.Text = "选择要导入的对比数据文件。支持Excel (.xlsx/.xls)、CSV (.csv) 和 JSON (.json) 格式。";
            //
            // lstFiles
            //
            this.lstFiles.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(45)))), ((int)(((byte)(45)))));
            this.lstFiles.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.lstFiles.ForeColor = System.Drawing.Color.White;
            this.lstFiles.FormattingEnabled = true;
            this.lstFiles.ItemHeight = 24;
            this.lstFiles.Location = new System.Drawing.Point(10, 60);
            this.lstFiles.Name = "lstFiles";
            this.lstFiles.SelectionMode = System.Windows.Forms.SelectionMode.MultiExtended;
            this.lstFiles.Size = new System.Drawing.Size(400, 450);
            this.lstFiles.TabIndex = 1;
            //
            // pnlButtons
            //
            this.pnlButtons.BackColor = System.Drawing.Color.Transparent;
            this.pnlButtons.Controls.Add(this.btnPreview);
            this.pnlButtons.Controls.Add(this.btnRemoveFiles);
            this.pnlButtons.Controls.Add(this.btnAddFiles);
            this.pnlButtons.Location = new System.Drawing.Point(420, 60);
            this.pnlButtons.Name = "pnlButtons";
            this.pnlButtons.Size = new System.Drawing.Size(100, 450);
            this.pnlButtons.TabIndex = 2;
            //
            // btnPreview
            //
            this.btnPreview.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(120)))), ((int)(((byte)(215)))));
            this.btnPreview.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnPreview.ForeColor = System.Drawing.Color.White;
            this.btnPreview.Location = new System.Drawing.Point(10, 100);
            this.btnPreview.Name = "btnPreview";
            this.btnPreview.Size = new System.Drawing.Size(80, 35);
            this.btnPreview.TabIndex = 2;
            this.btnPreview.Text = "预览数据";
            this.btnPreview.UseVisualStyleBackColor = false;
            //
            // btnRemoveFiles
            //
            this.btnRemoveFiles.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(60)))), ((int)(((byte)(60)))));
            this.btnRemoveFiles.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnRemoveFiles.ForeColor = System.Drawing.Color.White;
            this.btnRemoveFiles.Location = new System.Drawing.Point(10, 55);
            this.btnRemoveFiles.Name = "btnRemoveFiles";
            this.btnRemoveFiles.Size = new System.Drawing.Size(80, 35);
            this.btnRemoveFiles.TabIndex = 1;
            this.btnRemoveFiles.Text = "移除文件";
            this.btnRemoveFiles.UseVisualStyleBackColor = false;
            //
            // btnAddFiles
            //
            this.btnAddFiles.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(60)))), ((int)(((byte)(60)))));
            this.btnAddFiles.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnAddFiles.ForeColor = System.Drawing.Color.White;
            this.btnAddFiles.Location = new System.Drawing.Point(10, 10);
            this.btnAddFiles.Name = "btnAddFiles";
            this.btnAddFiles.Size = new System.Drawing.Size(80, 35);
            this.btnAddFiles.TabIndex = 0;
            this.btnAddFiles.Text = "添加文件";
            this.btnAddFiles.UseVisualStyleBackColor = false;
            //
            // dgvPreview
            //
            this.dgvPreview.AllowUserToAddRows = false;
            this.dgvPreview.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(45)))), ((int)(((byte)(45)))));
            this.dgvPreview.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.dgvPreview.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgvPreview.Location = new System.Drawing.Point(530, 60);
            this.dgvPreview.Name = "dgvPreview";
            this.dgvPreview.ReadOnly = true;
            this.dgvPreview.Size = new System.Drawing.Size(350, 450);
            this.dgvPreview.TabIndex = 3;
            //
            // progressBar
            //
            this.progressBar.Location = new System.Drawing.Point(10, 520);
            this.progressBar.Name = "progressBar";
            this.progressBar.Size = new System.Drawing.Size(870, 20);
            this.progressBar.Style = System.Windows.Forms.ProgressBarStyle.Continuous;
            this.progressBar.TabIndex = 4;
            this.progressBar.Visible = false;
            //
            // pnlBottomButtons
            //
            this.pnlBottomButtons.BackColor = System.Drawing.Color.Transparent;
            this.pnlBottomButtons.Controls.Add(this.btnCancel);
            this.pnlBottomButtons.Controls.Add(this.btnOK);
            this.pnlBottomButtons.Location = new System.Drawing.Point(10, 550);
            this.pnlBottomButtons.Name = "pnlBottomButtons";
            this.pnlBottomButtons.Size = new System.Drawing.Size(870, 50);
            this.pnlBottomButtons.TabIndex = 5;
            //
            // btnCancel
            //
            this.btnCancel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(60)))), ((int)(((byte)(60)))));
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnCancel.ForeColor = System.Drawing.Color.White;
            this.btnCancel.Location = new System.Drawing.Point(790, 10);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(80, 35);
            this.btnCancel.TabIndex = 1;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = false;
            //
            // btnOK
            //
            this.btnOK.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(120)))), ((int)(((byte)(215)))));
            this.btnOK.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.btnOK.Enabled = false;
            this.btnOK.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnOK.ForeColor = System.Drawing.Color.White;
            this.btnOK.Location = new System.Drawing.Point(700, 10);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(80, 35);
            this.btnOK.TabIndex = 0;
            this.btnOK.Text = "确定导入";
            this.btnOK.UseVisualStyleBackColor = false;
            //
            // BatchImportWizard
            //
            this.AutoScaleDimensions = new System.Drawing.SizeF(11F, 24F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(33)))), ((int)(((byte)(33)))), ((int)(((byte)(33)))));
            this.ClientSize = new System.Drawing.Size(900, 620);
            this.Controls.Add(this.pnlBottomButtons);
            this.Controls.Add(this.progressBar);
            this.Controls.Add(this.dgvPreview);
            this.Controls.Add(this.pnlButtons);
            this.Controls.Add(this.lstFiles);
            this.Controls.Add(this.lblInstructions);
            this.Font = new System.Drawing.Font("微软雅黑", 9F);
            this.ForeColor = System.Drawing.Color.White;
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "BatchImportWizard";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "批量导入对比数据向导";
            this.pnlButtons.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgvPreview)).EndInit();
            this.pnlBottomButtons.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Label lblInstructions;
        private System.Windows.Forms.ListBox lstFiles;
        private System.Windows.Forms.Panel pnlButtons;
        private System.Windows.Forms.Button btnPreview;
        private System.Windows.Forms.Button btnRemoveFiles;
        private System.Windows.Forms.Button btnAddFiles;
        private System.Windows.Forms.DataGridView dgvPreview;
        private System.Windows.Forms.ProgressBar progressBar;
        private System.Windows.Forms.Panel pnlBottomButtons;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnOK;
    }
}
