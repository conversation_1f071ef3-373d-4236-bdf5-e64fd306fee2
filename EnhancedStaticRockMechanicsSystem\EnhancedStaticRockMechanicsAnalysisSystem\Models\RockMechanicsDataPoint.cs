using System;

namespace EnhancedStaticRockMechanicsAnalysisSystem.Models
{
    /// <summary>
    /// 岩石力学数据点
    /// </summary>
    public class RockMechanicsDataPoint
    {
        /// <summary>
        /// 密度 (g/cm³)
        /// </summary>
        public double Density { get; set; }

        /// <summary>
        /// 纵波速度 (m/s)
        /// </summary>
        public double VpVelocity { get; set; }

        /// <summary>
        /// 横波速度 (m/s)
        /// </summary>
        public double VsVelocity { get; set; }

        /// <summary>
        /// 杨氏模量 (GPa)
        /// </summary>
        public double YoungModulus { get; set; }

        /// <summary>
        /// 泊松比
        /// </summary>
        public double PoissonRatio { get; set; }

        /// <summary>
        /// 脆性指数
        /// </summary>
        public double BrittlenessIndex { get; set; }

        /// <summary>
        /// 数据点ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 备注信息
        /// </summary>
        public string? Remarks { get; set; }

        public RockMechanicsDataPoint()
        {
        }

        public RockMechanicsDataPoint(double density, double vpVelocity, double vsVelocity)
        {
            Density = density;
            VpVelocity = vpVelocity;
            VsVelocity = vsVelocity;
        }

        /// <summary>
        /// 验证数据有效性
        /// </summary>
        /// <returns></returns>
        public bool IsValid()
        {
            return Density > 0 && VpVelocity > 0 && VsVelocity > 0 && VpVelocity > VsVelocity;
        }

        /// <summary>
        /// 获取数据点的字符串表示
        /// </summary>
        /// <returns></returns>
        public override string ToString()
        {
            return $"密度:{Density:F2}, Vp:{VpVelocity:F0}, Vs:{VsVelocity:F0}, 脆性指数:{BrittlenessIndex:F3}";
        }
    }
}
