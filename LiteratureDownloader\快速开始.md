# 📚 文献引用提取与下载系统 - 快速开始

## 🚀 如何运行程序

### 方法一：双击运行（推荐）
1. **Windows用户**：双击 `启动程序.bat`
2. **其他系统**：双击 `run.py`

### 方法二：命令行运行
```bash
python run.py
```

## 📋 使用步骤

### 1. 提取文献引用
**方式A：PDF文件识别**
- 点击"选择PDF文件"
- 选择包含参考文献的PDF
- 点击"识别PDF引用"

**方式B：截图识别**
- 点击"截图识别"
- 框选包含参考文献的区域
- 等待OCR识别完成

### 2. 下载文献
**方式A：简单自动下载（推荐）**
1. 勾选要下载的文献
2. 设置下载目录
3. 点击"简单自动下载"
4. 确认后等待自动完成

**方式B：打开搜索页面**
1. 勾选要搜索的文献
2. 点击"打开搜索页面"
3. 在打开的页面中手动下载

## ⚙️ 系统要求

### 必需软件
- Python 3.7+
- Chrome浏览器

### Python依赖包
```bash
pip install Pillow pytesseract selenium loguru
```

### 可选软件（用于截图识别）
- Tesseract OCR：[下载地址](https://github.com/UB-Mannheim/tesseract/wiki)

## 🔧 常见问题

### Q1: 程序无法启动
**A**: 检查Python版本和依赖包安装

### Q2: 截图识别不准确
**A**: 
- 确保截图清晰
- 安装Tesseract OCR
- 尝试调整截图区域

### Q3: 下载失败
**A**: 
- 确保已登录Web of Science
- 检查网络连接
- 尝试手动搜索方式

### Q4: 找不到ChromeDriver
**A**: 
- 程序会自动下载ChromeDriver
- 或手动下载放在系统PATH中

## 📞 技术支持

如遇问题，请：
1. 查看程序日志
2. 尝试重启程序
3. 检查网络和登录状态

## 🎯 使用技巧

1. **提高识别准确率**：
   - 选择清晰的PDF文件
   - 截图时包含完整的引用信息
   - 避免表格和图片干扰

2. **提高下载成功率**：
   - 确保Web of Science登录状态
   - 选择信息完整的文献
   - 使用校园网访问

3. **批量处理**：
   - 可以一次选择多个文献
   - 建议每次不超过10篇
   - 注意数据库下载限制

---

**开始使用吧！双击 `启动程序.bat` 或 `run.py` 即可启动程序。**
