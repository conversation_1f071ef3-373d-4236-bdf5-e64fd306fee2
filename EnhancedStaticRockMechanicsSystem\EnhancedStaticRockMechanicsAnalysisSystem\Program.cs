using System;
using System.Windows.Forms;
using EnhancedStaticRockMechanicsAnalysisSystem.Forms;
using EnhancedStaticRockMechanicsAnalysisSystem.Services;

namespace EnhancedStaticRockMechanicsAnalysisSystem
{
    /// <summary>
    /// 增强版静态岩石力学参数分析系统主程序
    /// </summary>
    internal static class Program
    {
        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static void Main()
        {
            try
            {
                // 启用应用程序视觉样式
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);

                // 记录系统启动
                LoggingService.Instance.LogSystemStart();

                // 清理旧日志文件
                LoggingService.Instance.CleanupOldLogs();

                // 设置全局异常处理
                Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);
                Application.ThreadException += Application_ThreadException;
                AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;

                LoggingService.Instance.Info("开始显示登录窗体");

                // 显示登录窗体
                using (var loginForm = new LoginForm())
                {
                    DialogResult loginResult = loginForm.ShowDialog();

                    if (loginResult == DialogResult.OK)
                    {
                        // 登录成功，显示主界面
                        string username = loginForm.Username;
                        LoggingService.Instance.LogUserAction(username, "登录成功");

                        using (var dashboardForm = new DashboardForm(username))
                        {
                            Application.Run(dashboardForm);
                        }
                    }
                    else
                    {
                        // 登录失败或取消，退出程序
                        LoggingService.Instance.Info("用户取消登录，程序退出");
                    }
                }

                // 记录系统关闭
                LoggingService.Instance.LogSystemShutdown();
            }
            catch (Exception ex)
            {
                // 记录启动失败
                LoggingService.Instance.Error("系统启动失败", ex);
                
                // 显示错误消息
                MessageBox.Show($"系统启动失败:\n{ex.Message}\n\n详细信息请查看日志文件。", 
                    "系统错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 处理UI线程异常
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private static void Application_ThreadException(object sender, System.Threading.ThreadExceptionEventArgs e)
        {
            try
            {
                LoggingService.Instance.Error("UI线程发生未处理异常", e.Exception);
                
                string message = $"程序发生错误:\n{e.Exception.Message}\n\n是否继续运行程序？";
                DialogResult result = MessageBox.Show(message, "程序错误", 
                    MessageBoxButtons.YesNo, MessageBoxIcon.Error);
                
                if (result == DialogResult.No)
                {
                    Application.Exit();
                }
            }
            catch (Exception ex)
            {
                // 如果连日志都无法写入，直接显示消息框
                MessageBox.Show($"严重错误:\n{ex.Message}", "系统错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                Application.Exit();
            }
        }

        /// <summary>
        /// 处理非UI线程异常
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private static void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            try
            {
                if (e.ExceptionObject is Exception exception)
                {
                    LoggingService.Instance.Error("应用程序域发生未处理异常", exception);
                    
                    string message = $"程序发生严重错误:\n{exception.Message}\n\n程序将退出。";
                    MessageBox.Show(message, "严重错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
                else
                {
                    LoggingService.Instance.Error($"应用程序域发生未知异常: {e.ExceptionObject}");
                    MessageBox.Show("程序发生未知错误，程序将退出。", "严重错误", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch
            {
                // 最后的保护措施
                MessageBox.Show("程序发生严重错误，程序将退出。", "严重错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                Environment.Exit(1);
            }
        }
    }
}
