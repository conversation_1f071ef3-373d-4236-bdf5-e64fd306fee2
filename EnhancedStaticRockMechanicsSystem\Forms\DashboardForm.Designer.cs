namespace EnhancedStaticRockMechanicsSystem.Forms
{
    partial class DashboardForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            lblTitle = new Label();
            btnStaticRockMechanics = new Button();
            btnExit = new Button();
            SuspendLayout();
            // 
            // lblTitle
            // 
            lblTitle.Dock = DockStyle.Top;
            lblTitle.Font = new Font("微软雅黑", 16F, FontStyle.Bold);
            lblTitle.ForeColor = Color.Cyan;
            lblTitle.Location = new Point(0, 0);
            lblTitle.Name = "lblTitle";
            lblTitle.Padding = new Padding(0, 10, 0, 0);
            lblTitle.Size = new Size(578, 73);
            lblTitle.TabIndex = 0;
            lblTitle.Text = "增强版静态岩石力学参数法系统V2.0";
            lblTitle.TextAlign = ContentAlignment.TopCenter;
            // 
            // btnStaticRockMechanics
            // 
            btnStaticRockMechanics.BackColor = Color.FromArgb(50, 50, 50);
            btnStaticRockMechanics.FlatAppearance.BorderColor = Color.Cyan;
            btnStaticRockMechanics.FlatStyle = FlatStyle.Flat;
            btnStaticRockMechanics.Font = new Font("微软雅黑", 12F);
            btnStaticRockMechanics.ForeColor = Color.LightSkyBlue;
            btnStaticRockMechanics.Location = new Point(214, 100);
            btnStaticRockMechanics.Name = "btnStaticRockMechanics";
            btnStaticRockMechanics.Size = new Size(150, 100);
            btnStaticRockMechanics.TabIndex = 1;
            btnStaticRockMechanics.Text = "开始分析\r\n静态岩石力学参数法";
            btnStaticRockMechanics.UseVisualStyleBackColor = false;
            btnStaticRockMechanics.Click += BtnStaticRockMechanics_Click;
            // 
            // btnExit
            // 
            btnExit.BackColor = Color.FromArgb(60, 60, 60);
            btnExit.FlatAppearance.BorderColor = Color.Cyan;
            btnExit.FlatStyle = FlatStyle.Flat;
            btnExit.Font = new Font("微软雅黑", 10F);
            btnExit.ForeColor = Color.White;
            btnExit.Location = new Point(235, 230);
            btnExit.Name = "btnExit";
            btnExit.Size = new Size(114, 40);
            btnExit.TabIndex = 2;
            btnExit.Text = "退出系统";
            btnExit.UseVisualStyleBackColor = false;
            btnExit.Click += BtnExit_Click;
            // 
            // DashboardForm
            // 
            BackColor = Color.FromArgb(33, 33, 33);
            ClientSize = new Size(578, 300);
            Controls.Add(lblTitle);
            Controls.Add(btnStaticRockMechanics);
            Controls.Add(btnExit);
            Name = "DashboardForm";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "增强版静态岩石力学参数法系统V2.0";
            ResumeLayout(false);
        }

        #endregion
    }
}
