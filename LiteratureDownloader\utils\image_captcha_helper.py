#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像验证码辅助工具
提供图像验证码的智能识别和用户交互界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import os
import time
import threading
from PIL import Image, ImageTk
import logging

logger = logging.getLogger(__name__)

class ImageCaptchaHelper:
    """图像验证码辅助工具"""
    
    def __init__(self):
        self.root = None
        self.result = False
        self.screenshot_path = ""
        
    def show_captcha_helper(self, screenshot_path: str = "", captcha_text: str = "") -> bool:
        """显示验证码辅助界面"""
        self.screenshot_path = screenshot_path
        self.result = False
        
        # 创建主窗口
        self.root = tk.Tk()
        self.root.title("🖼️ 图像验证码处理助手")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 设置窗口图标和样式
        self.root.configure(bg='#f0f0f0')
        
        # 创建界面
        self._create_interface(captcha_text)
        
        # 居中显示窗口
        self._center_window()
        
        # 显示窗口
        self.root.attributes('-topmost', True)
        self.root.focus_force()
        self.root.mainloop()
        
        return self.result
    
    def _create_interface(self, captcha_text: str):
        """创建用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(
            main_frame, 
            text="🖼️ 图像验证码处理助手",
            font=('Arial', 16, 'bold')
        )
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 说明文本
        info_text = """检测到复杂的图像选择验证码！

这种验证码需要根据提示选择正确的图像。请按照以下步骤操作：

📋 操作步骤：
1. 仔细阅读验证码页面的提示文字
2. 根据提示要求选择正确的图像
3. 点击所有符合要求的图像（通常会高亮显示）
4. 点击验证按钮完成验证
5. 完成后回到本窗口点击"验证完成"

💡 常见提示解读：
• "选择包含汽车的图像" → 选择有汽车的图片
• "选择交通工具" → 选择汽车、摩托车、自行车等
• "选择引擎才能工作的物体" → 选择汽车、船只、飞机等机械设备
• "选择道路标志" → 选择交通标志、路牌等

⚠️ 注意事项：
• 有些图像可能部分遮挡，仔细观察
• 如果不确定，可以尝试点击，错误选择通常可以再次点击取消
• 验证失败时可以点击刷新按钮重新获取验证码"""
        
        info_frame = ttk.LabelFrame(main_frame, text="📖 使用说明", padding="15")
        info_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 20))
        
        info_display = scrolledtext.ScrolledText(
            info_frame, 
            height=15, 
            wrap=tk.WORD,
            font=('Arial', 10),
            bg='#f8f8f8'
        )
        info_display.insert(tk.END, info_text)
        info_display.config(state=tk.DISABLED)
        info_display.pack(fill=tk.BOTH, expand=True)
        
        # 截图显示区域
        if self.screenshot_path and os.path.exists(self.screenshot_path):
            self._add_screenshot_display(main_frame)
        
        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=(20, 0))
        
        # 打开截图按钮
        if self.screenshot_path and os.path.exists(self.screenshot_path):
            screenshot_btn = ttk.Button(
                button_frame,
                text="📸 查看验证码截图",
                command=self._open_screenshot
            )
            screenshot_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 验证完成按钮
        complete_btn = ttk.Button(
            button_frame,
            text="✅ 验证完成",
            command=self._on_complete,
            style='Accent.TButton'
        )
        complete_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 跳过按钮
        skip_btn = ttk.Button(
            button_frame,
            text="⏭️ 跳过验证",
            command=self._on_skip
        )
        skip_btn.pack(side=tk.LEFT)
        
        # 配置样式
        style = ttk.Style()
        style.configure('Accent.TButton', foreground='white', background='#0078d4')
        
        # 配置网格权重
        main_frame.rowconfigure(1, weight=1)
    
    def _add_screenshot_display(self, parent):
        """添加截图显示区域"""
        try:
            screenshot_frame = ttk.LabelFrame(parent, text="📸 验证码截图预览", padding="10")
            screenshot_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))
            
            # 加载并显示截图
            image = Image.open(self.screenshot_path)
            # 调整图像大小以适应显示
            image.thumbnail((600, 300), Image.Resampling.LANCZOS)
            photo = ImageTk.PhotoImage(image)
            
            image_label = ttk.Label(screenshot_frame, image=photo)
            image_label.image = photo  # 保持引用
            image_label.pack()
            
        except Exception as e:
            logger.debug(f"显示截图失败: {e}")
    
    def _center_window(self):
        """居中显示窗口"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def _open_screenshot(self):
        """打开截图文件"""
        try:
            if os.name == 'nt':  # Windows
                os.startfile(self.screenshot_path)
            else:  # Linux/Mac
                import subprocess
                subprocess.run(['xdg-open', self.screenshot_path])
        except Exception as e:
            messagebox.showerror("错误", f"无法打开截图文件: {e}")
    
    def _on_complete(self):
        """验证完成"""
        result = messagebox.askyesno(
            "确认",
            "请确认您已经在浏览器中完成了图像验证？\n\n点击\"是\"表示验证已完成\n点击\"否\"返回继续操作",
            icon='question'
        )
        
        if result:
            self.result = True
            self.root.destroy()
    
    def _on_skip(self):
        """跳过验证"""
        result = messagebox.askyesno(
            "确认跳过",
            "确定要跳过此验证码吗？\n\n跳过可能导致无法正常访问目标网站。",
            icon='warning'
        )
        
        if result:
            self.result = False
            self.root.destroy()

def show_image_captcha_dialog(screenshot_path: str = "", captcha_text: str = "") -> bool:
    """显示图像验证码对话框"""
    helper = ImageCaptchaHelper()
    return helper.show_captcha_helper(screenshot_path, captcha_text)

if __name__ == "__main__":
    # 测试界面
    result = show_image_captcha_dialog()
    print(f"用户选择结果: {result}")
