#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试自定义浏览器路径功能
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from modules.config_manager import config_manager
from modules.simple_downloader import SimpleDownloader
from loguru import logger

def find_chrome_paths():
    """查找系统中的Chrome浏览器路径"""
    possible_paths = [
        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
        r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.getenv('USERNAME', '')),
        r"C:\Program Files\Microsoft\Edge\Application\msedge.exe",
        r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe",
    ]
    
    found_browsers = []
    for path in possible_paths:
        if os.path.exists(path):
            found_browsers.append(path)
    
    return found_browsers

def test_config_manager():
    """测试配置管理器"""
    print("=== 测试配置管理器 ===")
    
    # 测试获取默认配置
    print(f"默认下载目录: {config_manager.get_download_dir()}")
    print(f"浏览器配置: {config_manager.get_browser_config()}")
    
    # 测试设置浏览器配置
    chrome_paths = find_chrome_paths()
    if chrome_paths:
        test_path = chrome_paths[0]
        print(f"测试设置浏览器路径: {test_path}")
        config_manager.set_browser_config(test_path, True)
        
        # 验证设置
        browser_config = config_manager.get_browser_config()
        print(f"设置后的浏览器配置: {browser_config}")
        
        return test_path
    else:
        print("未找到Chrome浏览器")
        return None

def test_simple_downloader(browser_path=None):
    """测试简单下载器"""
    print("\n=== 测试简单下载器 ===")
    
    # 测试数据
    test_citations = [
        {
            'reference_number': '1',
            'title': 'Machine learning in academic research',
            'authors': 'Smith, J., Johnson, A.',
            'year': '2023',
            'journal': 'Nature'
        }
    ]
    
    # 设置下载目录
    download_dir = str(project_root / "downloads" / "test")
    os.makedirs(download_dir, exist_ok=True)
    
    print(f"下载目录: {download_dir}")
    print(f"自定义浏览器路径: {browser_path}")
    
    # 创建下载器
    downloader = SimpleDownloader(download_dir, browser_path)
    
    try:
        print("测试创建浏览器实例...")
        if downloader.create_new_browser():
            print("✅ 浏览器创建成功")
            
            # 测试打开Web of Science
            print("测试打开Web of Science...")
            downloader.driver.get("https://webofscience.clarivate.cn")
            
            input("请检查浏览器是否正常打开，按回车键继续...")
            
        else:
            print("❌ 浏览器创建失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    finally:
        # 清理
        downloader.close()
        print("测试完成")

def main():
    """主函数"""
    print("🔧 自定义浏览器路径功能测试")
    print("=" * 40)
    
    # 查找Chrome浏览器
    print("查找系统中的浏览器...")
    chrome_paths = find_chrome_paths()
    
    if chrome_paths:
        print("找到以下浏览器:")
        for i, path in enumerate(chrome_paths, 1):
            print(f"  {i}. {path}")
    else:
        print("未找到Chrome或Edge浏览器")
        return
    
    # 测试配置管理器
    browser_path = test_config_manager()
    
    # 询问是否测试下载器
    if input("\n是否测试下载器功能？(y/n): ").lower() == 'y':
        test_simple_downloader(browser_path)
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    main()
