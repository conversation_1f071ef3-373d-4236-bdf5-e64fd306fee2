{"format": 1, "restore": {"F:\\1-work\\2025\\2025-6\\6-22\\BritSystem\\EnhancedStaticRockMechanicsSystem\\EnhancedStaticRockMechanicsSystem.csproj": {}}, "projects": {"F:\\1-work\\2025\\2025-6\\6-22\\BritSystem\\EnhancedStaticRockMechanicsSystem\\EnhancedStaticRockMechanicsSystem.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\1-work\\2025\\2025-6\\6-22\\BritSystem\\EnhancedStaticRockMechanicsSystem\\EnhancedStaticRockMechanicsSystem.csproj", "projectName": "EnhancedStaticRockMechanicsSystem", "projectPath": "F:\\1-work\\2025\\2025-6\\6-22\\BritSystem\\EnhancedStaticRockMechanicsSystem\\EnhancedStaticRockMechanicsSystem.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\1-work\\2025\\2025-6\\6-22\\BritSystem\\EnhancedStaticRockMechanicsSystem\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2019SDK\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"NPOI": {"target": "Package", "version": "[2.6.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.Data.SqlClient": {"target": "Package", "version": "[4.8.6, )"}, "System.Windows.Forms.DataVisualization": {"target": "Package", "version": "[1.0.0-prerelease.20110.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}}}