using System;
using System.Windows.Forms;
using System.Drawing;

namespace EnhancedStaticRockMechanicsSystem.Forms
{
    public partial class LoginForm : Form
    {
        private TextBox txtUsername;
        private TextBox txtPassword;
        private Button btnLogin;
        private Label lblTitle;
        private Label lblSubtitle;
        private Label lblUsername;
        private Label lblPassword;
        private Label lblError;
        private bool loginSuccessful = false;

        // 请求的窗体名称，用于在登录成功后决定打开哪个窗体
        public string RequestedForm { get; private set; } = string.Empty;
        public string Username { get; private set; }

        public LoginForm()
        {
            InitializeComponent();
        }

        private void BtnLogin_Click(object sender, EventArgs e)
        {
            Username = txtUsername.Text.Trim();
            string password = txtPassword.Text.Trim();

            // 验证输入
            if (string.IsNullOrEmpty(Username) || string.IsNullOrEmpty(password))
            {
                ShowError("用户名和密码不能为空");
                return;
            }

            // 验证用户名和密码
            if (Username == "admin" && password == "123")
            {
                loginSuccessful = true;
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            else
            {
                ShowError("用户名或密码错误");
                txtPassword.Clear();
                txtPassword.Focus();
            }
        }

        private void ShowError(string message)
        {
            lblError.Text = message;
            lblError.Visible = true;
        }

        private void LoginForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            // 如果不是通过成功登录关闭窗体，则设置DialogResult为Cancel
            if (!loginSuccessful && this.DialogResult != DialogResult.OK)
            {
                this.DialogResult = DialogResult.Cancel;
            }
        }
    }
}
