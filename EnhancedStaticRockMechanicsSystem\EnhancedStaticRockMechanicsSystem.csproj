<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
    <!-- 禁用默认项目项 -->
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
    <EnableDefaultEmbeddedResourceItems>false</EnableDefaultEmbeddedResourceItems>
    <!-- 禁用自动生成资源名称 -->
    <GenerateResourceMSBuildArchitecture>CurrentArchitecture</GenerateResourceMSBuildArchitecture>
    <GenerateResourceMSBuildRuntime>CurrentRuntime</GenerateResourceMSBuildRuntime>
    <!-- 强制使用显式指定的ManifestResourceName -->
    <EmbeddedResourceUseDependentUponConvention>false</EmbeddedResourceUseDependentUponConvention>
    <!-- 高DPI支持设置 -->
    <ApplicationHighDpiMode>PerMonitorV2</ApplicationHighDpiMode>
    <AssemblyTitle>增强版静态岩石力学参数法系统</AssemblyTitle>
    <AssemblyDescription>基于静态岩石力学参数的脆性指数计算系统 - 增强版</AssemblyDescription>
    <AssemblyCompany>BritSystem</AssemblyCompany>
    <AssemblyProduct>增强版静态岩石力学参数法系统</AssemblyProduct>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <StartupObject>EnhancedStaticRockMechanicsSystem.Program</StartupObject>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <DefineConstants>TRACE</DefineConstants>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="NPOI" Version="2.6.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
    <PackageReference Include="System.Windows.Forms.DataVisualization" Version="1.0.0-prerelease.20110.1" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Resources\" />
    <Folder Include="SampleData\" />
    <Folder Include="Documentation\" />
  </ItemGroup>

  <ItemGroup>
    <!-- 程序入口 -->
    <Compile Include="Program.cs" />

    <!-- 配置和服务类 -->
    <Compile Include="AppConfig.cs" />
    <Compile Include="Core\DataManager.cs" />
    <Compile Include="Services\LoggingService.cs" />
    <Compile Include="Services\UnifiedComparisonDataManager.cs" />
    <Compile Include="Services\ComparisonFileParser.cs" />
    <Compile Include="Services\ExtendedCsvImportService.cs" />
    <Compile Include="Services\ExtendedExcelImportService.cs" />
    <Compile Include="Services\ImageAssociationService.cs" />
    <Compile Include="Services\ImportService.cs" />

    <!-- 数据模型 -->
    <Compile Include="Models\BrittlenessDataPoint.cs" />
    <Compile Include="Models\ComparisonDataSet.cs" />
    <Compile Include="Models\RockMechanicsDataPoint.cs" />
    <Compile Include="Models\CalculationResult.cs" />

    <!-- 窗体文件 -->
    <Compile Include="Forms\LoginForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\LoginForm.Designer.cs">
      <DependentUpon>LoginForm.cs</DependentUpon>
    </Compile>

    <Compile Include="Forms\DashboardForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\DashboardForm.Designer.cs">
      <DependentUpon>DashboardForm.cs</DependentUpon>
    </Compile>

    <Compile Include="Forms\TestDesignerForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\TestDesignerForm.Designer.cs">
      <DependentUpon>TestDesignerForm.cs</DependentUpon>
    </Compile>

    <Compile Include="Forms\BatchImportWizard.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\BatchImportWizard.Designer.cs">
      <DependentUpon>BatchImportWizard.cs</DependentUpon>
    </Compile>

    <Compile Include="Forms\EnhancedComparisonChartForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\EnhancedComparisonChartForm.Designer.cs">
      <DependentUpon>EnhancedComparisonChartForm.cs</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>

    <EmbeddedResource Include="Forms\BatchImportWizard.resx">
      <DependentUpon>BatchImportWizard.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\EnhancedComparisonChartForm.resx">
      <DependentUpon>EnhancedComparisonChartForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\LoginForm.resx">
      <DependentUpon>LoginForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\DashboardForm.resx">
      <DependentUpon>DashboardForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\TestDesignerForm.resx">
      <DependentUpon>TestDesignerForm.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>

  <ItemGroup>
    <None Include="README.md" />
    <None Include="使用说明.md" />
    <None Include="启动系统.bat" />
  </ItemGroup>

</Project>
